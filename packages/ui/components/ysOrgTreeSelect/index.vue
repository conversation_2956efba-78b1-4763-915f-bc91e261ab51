<script lang="ts" setup>
/* 
  机构加载下拉树
*/
import { ref, onMounted } from "vue";
import { request, CommonRes, common } from "@ys/tools";
import type { TreeSelectProps } from "ant-design-vue";
const { getUrlQuery } = common;
type IOrg = {
  orgName: string;
  parentId: string;
  id: string;
};

const props = defineProps<{
  value: string;
  orgId?: string;
}>();

const emit = defineEmits(["update:value", "change"]);
const treeData = ref<TreeSelectProps["treeData"]>([]);

async function getList() {
  let bureauId = getUrlQuery("bureauId") || 0;
  let orgId = props.orgId || bureauId;
  let result = await request.commonApi.getAllOrg<CommonRes<IOrg[]>>({ orgId });
  const list = result.data.data.map((org) => ({
    id: org.id,
    value: org.id,
    pId: org.parentId,
    title: org.orgName,
  }));
  treeData.value = list;
}
onMounted(getList);
function handleChange(value: string) {
  emit("update:value", value);
  emit("change", value);
}
</script>

<template>
  <a-tree-select
    :allowClear="true"
    :value="value || null"
    @change="handleChange"
    tree-data-simple-mode
    style="width: 100%"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    :tree-data="treeData"
    placeholder="请选择机构"
  />
</template>
