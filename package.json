{"name": "v-turbo", "version": "0.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev --parallel", "build:BASE": "cross-env BASE=https://esepage.yscdn.top/yskt/ys turbo run build", "build": "turbo run build", "lint": "turbo run lint"}, "devDependencies": {"cross-env": "^7.0.3", "prettier": "latest", "turbo": "latest"}, "engines": {"node": ">=14.0.0"}, "packageManager": "pnpm@7.5.0", "dependencies": {"vite-plugin-html": "^3.2.2"}}