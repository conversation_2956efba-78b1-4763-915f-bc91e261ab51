<template>
  <div class="box flex">
    <div
      class="scrollbars p1624 h100"
      style="
        width: 294px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-right: 1px solid rgba(0, 0, 0, 0.1);
      "
    >
      <a-tree
        :field-names="fieldNames"
        :load-data="handleonLoadData"
        :tree-data="treeData"
        @select="handleClickSelect"
      ></a-tree>
    </div>
    <div
      style="width: 290px; border-top: 1px solid rgba(0, 0, 0, 0.1)"
      class="altList p1624 scrollbars"
    >
      <div class="altheader">
        <span style="font-weight: bold">待选列表</span>
        <a-checkbox
          v-model:checked="selectAllPeople"
          :indeterminate="indeterminate"
          @change="handlepeopleCheckAllChange"
        >
          全选
        </a-checkbox>
      </div>
      <a-input-search
        v-model:value="keywords"
        placeholder="请输入人员名称"
        class="search-input-basic"
        style="width: 100%; margin-right: 24px; margin-bottom: 24px"
        @search="handleonSearchpeopleAtl"
      />
      <div style="width: 100%" class="scrollbars">
        <ul class="checkBoxList scrollbar">
          <li v-for="item in casualList" :key="item.key" :span="24">
            <a-checkbox @change="getchange(item)" v-model:checked="item.status">
              {{ item.label }}</a-checkbox
            >
          </li>
        </ul>
      </div>
    </div>
    <div
      style="
        width: 294px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        border-right: 1px solid rgba(0, 0, 0, 0.1);
      "
      class="chooseList flex1 scrollbars"
    >
      <div class="altheader">
        <span style="font-weight: bold"
          >已选列表 {{ " " + choosePeopleList.length }}</span
        >
        <a-button type="link" @click="handleClear">清空</a-button>
      </div>
      <ul class="chooseList-content scrollbar">
        <li v-for="(item, index) in choosePeopleList" :key="index">
          <span>{{ item.label }}</span>
          <ysIcon
            @click="handleCloseOnePeople(index, item)"
            type="icon-close"
          ></ysIcon>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from "vue";
import { TreeProps } from "ant-design-vue";
import {
  setinggetAllOrg,
  teacherInfoGetKeyAndValue,
  getAllOrg,
  getUserInfo,
  findOrgById,
} from "./../api/index";
import { ysIcon } from "@ys/ui";
import {
  getOrgDetail,
  getOrg,
  department_tree,
  teacherInfoBase,
} from "./../api/index";
import { request, CommonRes, common } from "@ys/tools";
const { data2Tree, getSessionUser } = common;
const userInfo = getSessionUser();
const { commonApi } = request;
type IOrg = {
  orgName: string;
  parentId: string;
  id: string;
};
const props = defineProps<{
  list: Array<any>;
}>();
const emit = defineEmits(["handleChange"]);

const fieldNames: TreeProps["fieldNames"] = {
  children: "children",
  title: "displayName",
  key: "id",
};
/* --------------- data --------------- */
//#region
const treeData = ref<any>([]);
/* 全选/取消状态 */
const selectAllPeople = ref<boolean>(false);
/* 半选状态 */
const indeterminate = ref<boolean>(false);
/* 关键字 */
const keywords = ref<string>("");
/* 选择的机构id */
const selectId = ref<string[]>([]);
const orgID = ref<string>("");
/* 机构下所有用户 */
const userList = ref<Array<any>>([]);
/* 临时数据，做本地筛选 */
const casualList = ref<Array<any>>([]);
/* 已选人员 */
const choosePeopleList = ref<any[]>([]);
watch(
  () => props.list,
  (val: any) => {
    choosePeopleList.value = val;
  },
  { immediate: true }
);

//#endregion

/* --------------- methods --------------- */
//#region

onMounted(async () => {
  // handleGetPeopleOrglist('0')
  await handlegetUserInfo();
  // await getList()
  handleGetOneOrg();
});

async function handlegetUserInfo() {
  const res: any = await getUserInfo();
  console.log(res);
  orgID.value = res.data.data.orgId;
}

//组织机构树数据
const handleGetOneOrg = () => {
  const params = {
    id: orgID.value,
  };
  getOrgDetail(params).then((res: any) => {
    const data = res.data.data;
    let obj = [
      {
        displayName: data.displayName,
        id: orgID.value,
        value: orgID.value,
        nodeType: 1,
        next: true,
      },
    ];
    treeData.value = obj;
  });
};

const handleonLoadData = (parent: any) => {
  console.log(parent);
  return new Promise((resolve: any, reject: any) => {
    // if (parent.dataRef.next) {
    //     resolve();
    //     return;
    // }
    const params = {
      level: 1,
      onlyOrg: 0,
      parentId: parent.dataRef.value,
    };
    getOrg(params).then((res: any) => {
      const data = res.data.data;
      parent.dataRef.children = data;
      data.map((item: any) => {
        (item.title = item.displayName),
          (item.value = item.id),
          (item.next = item.next),
          (item.isLeaf = !item.next ? true : false),
          (item.nodeType = item.nodeType);
      });
      treeData.value = [...treeData.value];
      resolve();
    });
  });
};

// async function getList() {
//     let result: any = null;
//     result = await commonApi.getAllOrg<CommonRes<IOrg[]>>({ orgId: orgID.value });
//     console.log(result.data.data)
//     let list = result.data.data
//     for (let i = 0; i < list.length; i++) {
//         if (list[i].parentId == 0 && !list[i].paths) {
//             orgID.value = list[i].id
//         }
//     }

// }
//选择机构
const handleClickSelect = (e: any, e2: any) => {
  /* 阻止重复点击 */
  // if (selectedKeys.length == 0 || selectedKeys[0] == selectId.value) {
  //     return;
  // }
  console.log(e, e2.node);
  if (e2.node.nodeType == 1) {
    handleGetPeopleListOrg(e[0]);
  }
  if (e2.node.nodeType == 0) {
    // handleGetPeopleListOrg(e[0]);
    handleGetUserBySection(e[0]);
  }
  // selectId.value = selectedKeys;
};

function handleGetUserBySection(id: any) {
  const params = {
    pageIndex: 1,
    pageSize: 1000,
    orgId: id,
  };
  findOrgById(params).then((res: any) => {
    const data = res.data.data || [];
    userList.value = data.map((item: any) => {
      return {
        label: item.userName,
        value: item.userInfoId,
        name: item.userName,
        id: item.accountId,
      };
    });
    casualList.value = JSON.parse(JSON.stringify(userList.value));
    console.log(casualList.value);
    refreshChedked();
  });
}

/* 查询机构所有人员 */
const handleGetPeopleListOrg = (id: any) => {
  const params = {
    orgId: id,
  };
  teacherInfoGetKeyAndValue(params).then((res: any) => {
    if (res.data.code == 0) {
      const data = res.data.data;
      userList.value = data.map((item: any) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
      casualList.value = JSON.parse(JSON.stringify(userList.value));
      refreshChedked();
    }
  });
};

/* 全选当前列表的人员 */
const handlepeopleCheckAllChange = () => {
  choosePeopleList.value = [];
  if (selectAllPeople.value == true) {
    for (let i of casualList.value) {
      i.status = true;
      choosePeopleList.value.push(i);
    }
  } else {
    for (let i of casualList.value) {
      i.status = false;
    }
    userList.value.forEach((j) => {
      choosePeopleList.value.splice(
        choosePeopleList.value.findIndex((v) => v.value == j.value),
        1
      );
    });
  }
  refreshChedked();
};
/* 关键字查询 */
const handleonSearchpeopleAtl = () => {
  if (keywords.value) {
    casualList.value = userList.value.filter((v: any) =>
      v.label.includes(keywords.value)
    );
  } else casualList.value = JSON.parse(JSON.stringify(userList.value));
  refreshChedked();
};
/* 单个勾选人员 */
function getchange(val: any) {
  if (
    val.status &&
    !choosePeopleList.value.some((v: any) => v.value == val.value)
  ) {
    choosePeopleList.value.push(val);
  } else {
    choosePeopleList.value.splice(
      choosePeopleList.value.findIndex((v) => v.value == val.value),
      1
    );
  }
  refreshChedked();
}
// 清空已选人员
const handleClear = () => {
  choosePeopleList.value = [];
  selectAllPeople.value = false;
  for (let i of casualList.value) {
    i.status = false;
  }
  refreshChedked();
};
// 删除单个已选人员
const handleCloseOnePeople = (index: number, item: any) => {
  choosePeopleList.value.splice(index, 1);
  refreshChedked();
};
/* 更新当中复选框选中状态 */
function refreshChedked() {
  let selectLength = 0;
  casualList.value.forEach((v: any) => {
    v.status = false;
    if (choosePeopleList.value.some((v1: any) => v1.value == v.value)) {
      v.status = true;
      selectLength += 1;
    }
  });
  if (casualList.value.length == 0) {
    selectAllPeople.value = false;
    indeterminate.value = false;
  } else {
    if (selectLength == casualList.value.length) {
      selectAllPeople.value = true;
      indeterminate.value = false;
    } else if (selectLength == 0) {
      selectAllPeople.value = false;
      indeterminate.value = false;
    } else {
      indeterminate.value = true;
    }
  }
  emit("handleChange", choosePeopleList.value);
}
//#endregion
</script>

<style lang="scss" scoped>
ul,
li {
  list-style: none;
  padding-left: 0;
}

.scrollbars {
  overflow: auto;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: rgba(161, 168, 184, 0.3);
  }
}

.flex {
  display: flex;
}

.p1624 {
  padding: 16px 24px;
}

.h100 {
  height: 100%;
}

.flex1 {
  flex: 1;
}

.box {
  height: calc(100vh - 320px);
}

.footer {
  padding-right: 24px;
  height: 60px;
  line-height: 60px;
  text-align: right;
}

.altList {
  border-right: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
  width: 384px;

  .altheader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }
}

.chooseList {
  padding: 16px 0;
  border-bottom: 1px solid #e5e5e5;

  .altheader {
    height: 22px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 24px;
  }

  .chooseList-content {
    width: 100%;
    overflow: hidden;
    overflow-y: scroll;

    li {
      width: 100%;
      height: 44px;
      padding: 12px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      > i {
        opacity: 0;
      }

      &:hover {
        background: #f5f5f5;

        > i {
          opacity: 1;
          cursor: pointer;
        }
      }
    }
  }
}

.search-input-basic {
  .ant-input {
    height: 36px !important;
  }
}

.checkBoxList {
  width: 100%;
  overflow: hidden;
  overflow-y: scroll;

  li {
    width: 100%;
    height: 46px;
  }
}
</style>
