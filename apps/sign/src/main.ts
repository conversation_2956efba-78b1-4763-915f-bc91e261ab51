import { createApp } from "vue";
import App from "./App.vue";
import router from "./routes";
import Antd from "ant-design-vue";
import "ant-design-vue/dist/antd.less";
import { initial, common, CommonRes } from "@ys/tools";
import { getpublicconfig } from "@/api";
import Vconsole from "vconsole";
const { getUrlQuery } = common;
const openVconsole = getUrlQuery("openVconsole");
// 在测试的正式环境中
if (
  origin.indexOf(".ysclass.net") != -1 ||
  origin === "http://**************" ||
  origin.startsWith("https://ysqa") ||
  origin.startsWith("https://ysqb") ||
  openVconsole
) {
  new Vconsole();
}
// new Vconsole();
async function boostrap() {
  await initial.start();
  const app = createApp(App);
  app.use(Antd);
  app.use(router);
  app.mount("#app");
  // 注入微信配置 (暂时弃用，验证签到白屏，在业务内建权)
  // if (navigator.userAgent.indexOf("miniProgram") != -1) {
  // 	const tenantId = getUrlQuery("tenantId");
  // 	const bureauId = getUrlQuery("bureauId");
  // 	const result = await getpublicconfig<
  // 		CommonRes<{
  // 			appId: string;
  // 			nonceStr: string;
  // 			originalID: string;
  // 			signType: string;
  // 			signature: string;
  // 			timestamp: string;
  // 		}>
  // 	>(
  // 		{
  // 			orgId: bureauId,
  // 			tenantId,
  // 			thirdType: "wechat_service",
  // 			used: "sendMsg",
  // 			url: location.href.split("#")[0],
  // 		},
  // 		tenantId
  // 	);
  // 	if (result.data.code === 0) {
  // 		const { appId, timestamp, nonceStr, signature } = result.data.data;
  // 		(window as any).wx.config({
  // 			debug: false, // 调试时使用
  // 			appId, //这里是传入公众号唯一标识
  // 			timestamp, // 必填 任意数字即可
  // 			nonceStr, // 必填，生成签名的随机串
  // 			signature, // 必填，签名
  // 			jsApiList: ["scanQRCode", "getLocation"],
  // 			openTagList: ["wx-open-launch-weapp"], // 获取开放标签权限
  // 		});
  // 		(window as any).wx.ready(() => {
  // 			const app = createApp(App);
  // 			app.use(Antd);
  // 			app.use(router);
  // 			app.mount("#app");
  // 		});
  // 	}
  // } else {
  // 	const app = createApp(App);
  // 	app.use(Antd);
  // 	app.use(router);
  // 	app.mount("#app");
  // }
}
boostrap();
