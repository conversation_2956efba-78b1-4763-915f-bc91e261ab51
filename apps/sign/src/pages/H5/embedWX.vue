<script lang="ts" setup>
import { ref, onMounted, computed, nextTick } from "vue";
import { message } from "ant-design-vue";
import { useRoute, useRouter } from "vue-router";
import { CommonRes, common } from "@ys/tools";
import { ysIcon } from "@ys/ui";
import { getpublicconfig } from "@/api";
import { check_sign, get_signNew } from "@/api";
import {
  createMap,
  createMarker,
  createPosition,
  calculateDistance,
} from "./map";
const { getUrlQuery } = common;
const route = useRoute();
const router = useRouter();
const baseId = route.query.baseId;
const time = route.query.time;
const { getSessionUser } = common;
const userInfo = getSessionUser();

const signStatus = ref<1 | 2 | 3 | 5>(); // 1成功 2不在时间 3已签到  5不在范围
const signedInfo = ref<any>(null);

async function onSign() {
  if (!userInfo) {
    message.error("微信签到页无用户登录信息");
    return;
  }
  const { orgId, orgName, name, countName, id } = userInfo;
  const result = await check_sign<CommonRes<any>>({
    checkType: 1,
    signTaskBaseId: baseId,
    organizationId: orgId,
    organization: orgName,
    timeStamp: Date.now(),
    userName: name,
    mobile: countName,
    address: currentLocation.value?.address,
    card: id,
    userId: 0,
    refreshTime: time,
  });

  if (result.data.code === 0) {
    signStatus.value = 1;
  }
  if (result.data.code === -1) {
    signStatus.value = 2;
  }
  if (result.data.code === -2) {
    signStatus.value = 3;
  }
  signedInfo.value = result.data.data;

  if (result.data.code === 500 && result.data.msg === "暂无签到权限") {
    router.replace("/H5/noAuth");
  }
}
interface SignInfo {
  isPosition: 1 | 2; // 1.开定位 2.不开定位
  signAddress: string;
  signAddressRadius: number;
  signAddressX: string;
  signAddressY: string;
}
const signInfo = ref<SignInfo>();
async function getSignInfo() {
  if (!baseId) {
    console.log("参数获取失败，baseId failed");
    return;
  }
  const result = await get_signNew<CommonRes<SignInfo>>({ baseId });
  if (result.data.code === 0) {
    signInfo.value = result.data.data;
    // 没开定位
    if (signInfo.value.isPosition === 2) {
      onSign();
      return;
    }
    // 开定位、校验定位
    if (signInfo.value.isPosition === 1) {
      dispatchPositionByWX();
    }
  }
}
const AMap = (window as any).AMap;
var markers: any = [];
async function dispatchPositionByWX() {
  const tenantId = getUrlQuery("tenantId");
  const bureauId = getUrlQuery("bureauId");
  const result = await getpublicconfig<
    CommonRes<{
      appId: string;
      nonceStr: string;
      originalID: string;
      signType: string;
      signature: string;
      timestamp: string;
    }>
  >(
    {
      orgId: bureauId,
      tenantId,
      thirdType: "wechat_service",
      used: "sendMsg",
      url: location.href.split("#")[0],
    },
    tenantId
  );
  if (result.data.code === 0) {
    const { appId, timestamp, nonceStr, signature } = result.data.data;
    (window as any).wx.config({
      debug: false, // 调试时使用
      appId, //这里是传入公众号唯一标识
      timestamp, // 必填 任意数字即可
      nonceStr, // 必填，生成签名的随机串
      signature, // 必填，签名
      jsApiList: ["scanQRCode", "getLocation"],
      openTagList: ["wx-open-launch-weapp"], // 获取开放标签权限
    });
    (window as any).wx.ready(() => {
      handleGetLocation();
    });
  }
}

function handleGetLocation() {
  (window as any).wx.getLocation({
    type: "gcj02",
    success(data: any) {
      currentLocation.value = {
        city: "",
        address: signInfo.value?.signAddress || "",
        accuracy: data.accuracy,
        latitude: data.latitude,
        province: "",
        longitude: data.longitude,
      };
      if (!currentLocation.value) {
        signStatus.value = 5;
        nextTick(() => {
          if (!map && !marker) {
            map = new AMap.Map("tenxun_map", {
              resizeEnable: true,
              zoom: 11,
            });
            let position_new = new AMap.LngLat(data.longitude, data.latitude);

            const markerContent = `<div class="custom-content-marker">
					<img src="https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png">
				</div>`;
            let marker = new AMap.Marker({
              position: position_new,
              content: markerContent,
              offset: new AMap.Pixel(-13, -30),
            });
            markers.forEach((marker1: any) => {
              marker1.setMap(null);
            });
            markers = [];
            markers.push(marker);
            map.add(marker);
            map.setZoomAndCenter(15, position_new);
            // marker = createMarker(map);
          }
        });
        console.log("获取定位失败，请重试", data.res);
        return;
      }
      const inRightLocation = checkIsInCurrentLocation();
      console.log(inRightLocation);
      if (inRightLocation) {
        onSign();
      } else {
        signStatus.value = 5;
        nextTick(() => {
          nextTick(() => {
            if (!map && !marker) {
              map = new AMap.Map("tenxun_map", {
                resizeEnable: true,
                zoom: 11,
              });
              let position_new = new AMap.LngLat(data.longitude, data.latitude);

              const markerContent = `<div class="custom-content-marker">
					<img src="https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png">
				</div>`;
              let marker = new AMap.Marker({
                position: position_new,
                content: markerContent,
                offset: new AMap.Pixel(-13, -30),
              });
              markers.forEach((marker1: any) => {
                marker1.setMap(null);
              });
              markers = [];
              markers.push(marker);
              map.add(marker);
              map.setZoomAndCenter(15, position_new);
              // marker = createMarker(map);
            }
          });
          const { latitude, longitude } = currentLocation.value!;
          const position = createPosition(Number(latitude), Number(longitude));
          // map.panTo(position);
          // marker.setGeometries([{ position }]);
        });
      }
    },
    fail(error: any) {
      console.log("获取微信sdk-location失败", error);
    },
  });
}

function checkIsInCurrentLocation() {
  if (!currentLocation.value) {
    message.error("无当前定位信息，currentLocation为空");
    return;
  }
  if (!signInfo.value) {
    message.error("当前签到任务信息signInfo为空");
    return;
  }
  const d1 = {
    latitude: Number(currentLocation.value.latitude),
    longitude: Number(currentLocation.value.longitude),
  };
  const d2 = {
    latitude: Number(signInfo.value.signAddressX),
    longitude: Number(signInfo.value.signAddressY),
  };
  console.log(d1, d2);
  const dis = calculateDistance(d1, d2) * 1000;
  return dis < signInfo.value.signAddressRadius;
}

/* 获取真实位置 */
interface Location {
  city: string;
  address: string;
  accuracy: string;
  latitude: string;
  province: string;
  longitude: string;
}
const currentLocation = ref<Location>();

let map: any = null;
let marker: any = null;
onMounted(() => {
  if (!userInfo) {
    message.error("获取用户信息异常, 请重试");
    return;
  }
  getSignInfo();
});

// 签到日期
const signDate = computed(() => {
  if (!signedInfo.value) return "";

  if (signedInfo.value.signType === 3) {
    return "不限";
  }
  return false;
});

// 签到时段
const signTime = computed(() => {
  if (!signedInfo.value) return "";

  if (signedInfo.value.signType === 3) {
    return "不限";
  }
  return false;
});
// 格式化星期
const formatWerk = (args: string) => {
  const weeks = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
  const list = args.split(",");
  return list
    .map((item) => {
      return weeks[Number(item) - 1];
    })
    .join("、");
};
const formatSignTime = (times: { startTime: string; endTime: string }[]) => {
  const currentTime: string[] = [];
  times.forEach((time: any) => {
    const { startTime, endTime } = time;
    currentTime.push(`${startTime.substring(0, 5)}-${endTime.substring(0, 5)}`);
  });
  return currentTime.join("、");
};
</script>

<template>
  <div class="signInfo">
    <div class="signContent">
      <!-- 签到成功 -->
      <div v-if="signStatus === 1" class="signStatusContainer">
        <ysIcon class="icon" type="icongou2"></ysIcon>
        <div class="signLabel">签到成功</div>
        <div class="signTime">
          <span v-if="signedInfo.signType == '3'">{{
            signedInfo.currentDate.signDate
          }}</span>
          <span v-else>
            {{ signedInfo.currentDate.signDate }}&nbsp;&nbsp;{{
              signedInfo.currentTime.startTime.substring(0, 5)
            }}-{{ signedInfo.currentTime.endTime.substring(0, 5) }}
          </span>
        </div>
        <div v-if="currentLocation" class="signAddr text-overflow">
          {{ currentLocation?.address }}
        </div>
      </div>
      <!-- 不在签到时间 -->
      <div v-if="signStatus === 2" class="signStatusContainer">
        <ysIcon class="icon" type="iconxinxi" style="color: #ffaa00"></ysIcon>
        <div class="signLabel">不在签到时间内</div>
      </div>
      <!-- 已签到 -->
      <div v-if="signStatus === 3" class="signStatusContainer">
        <ysIcon class="icon" type="iconxinxi" style="color: #007aff"></ysIcon>
        <div class="signLabel">您已签到</div>
        <div class="signTime">
          <span v-if="signedInfo.signType != '3'">{{
            signedInfo.record.signTime
          }}</span>
        </div>
      </div>
      <!-- 不在正确定位 -->
      <div v-if="signStatus === 5" class="signStatusContainer">
        <ysIcon class="icon" type="iconxinxi" style="color: #ffaa00"></ysIcon>
        <div class="signLabel">未在签到范围内</div>
        <div class="signTime signBtn">
          <a-button @click="dispatchPositionByWX" type="primary" shape="round"
            >刷新位置</a-button
          >
        </div>
      </div>
      <!-- 地图 -->
      <div v-if="signStatus === 5" class="signBasicInfo">
        <div id="tenxun_map" class="mapContainer"></div>
      </div>

      <div v-if="signedInfo" class="signBasicInfo">
        <div class="item">
          <div class="label">任务名称</div>
          <div class="text-overflow">
            {{ signedInfo.task.taskName }}
          </div>
        </div>

        <div class="item" v-if="signDate">
          <div class="label">签到日期</div>
          <div>
            {{ signDate }}
          </div>
        </div>
        <div class="item" v-if="signTime">
          <div class="label">签到时段</div>
          <div>
            {{ signTime }}
          </div>
        </div>
        <template
          v-if="signedInfo.signType === 1"
          v-for="(list, index) in signedInfo.task.dates"
        >
          <div class="item">
            <div class="label">签到日期{{ index + 1 }}</div>
            <div>
              {{ list.signDate }}
            </div>
          </div>
          <div class="item">
            <div class="label">签到时段{{ index + 1 }}</div>
            <div>
              {{ formatSignTime(list.times) }}
            </div>
          </div>
        </template>
        <template
          v-if="signedInfo.signType === 2"
          v-for="(list, index) in signedInfo.task.dates"
        >
          <div class="item">
            <div class="label">签到日期{{ index + 1 }}</div>
            <div>
              {{ formatWerk(list.signWeek) }}
            </div>
          </div>
          <div class="item">
            <div class="label">签到时段{{ index + 1 }}</div>
            <div>
              {{ formatSignTime(list.times) }}
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.signInfo {
  padding: 16px;
  min-height: 100vh;
  background: #0a7efd;
}

.signContent {
  background: #fff;
  border-radius: 6px;
}

.signStatusContainer::before {
  content: "";
  position: absolute;
  bottom: -10px;
  left: -10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #0a7efd;
}

.signStatusContainer::after {
  content: "";
  position: absolute;
  bottom: -10px;
  right: -10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #0a7efd;
}

.signStatusContainer {
  position: relative;
  text-align: center;
  padding: 60px 30px;
  border-bottom: 1px dashed #d9d9d9;

  .icon {
    font-size: 70px;
  }

  .signLabel {
    margin: 12px 0 16px;
    font-weight: 500;
    font-size: 20px;
  }

  .signTime {
    font-weight: 400;
    font-size: 16px;
  }

  .signAddr {
    margin-top: 12px;
    font-weight: 400;
    color: #808080;
  }
}

.signBasicInfo {
  padding: 24px 16px 40px;

  .item {
    font-size: 16px;
    color: #4d4d4d;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0%;
    border-bottom: 1px solid #f5f5f5;

    div {
      &:last-child {
        color: #1a1a1a;
        text-align: right;
        flex: 1;
        margin-left: 20px;
      }
    }
  }

  .mapContainer {
    aspect-ratio: 1;
  }
}
</style>
