export type TASK = {
  lectureBaseTaskId: string;
  taskName: string;
  remark: string;
  startDate: string;
  endDate: string;
  locationOpen: 1 | 2;
  uploadRequire: 1 | 2;
  evaluateRequire: 1 | 2;
  useTable: 1 | 2;
  patrolOrgRequire: 1 | 2;
  patrolOrgShow: 1 | 2;
  patrolUserRequire: 1 | 2;
  patrolUserShow: 1 | 2;
  formInfoId: "";
  formInfoName: "";
  formTemplateInfoId: "";
};

export type Record = {
  attrList: any;
  taskName: string;
  submitTime: string;
  recoreUserName: string;
  recordOrgId: string;
  recordOrgName: string;
  submitAddress: string;
  lectureBaseTaskId: string;
  lectureTaskRecordId: string;
  lectureComment: string;
  formTemplateInfoId: string;
  formAnswer: string;
  formInfoId: string;
  useTable: 1 | 2;
};
