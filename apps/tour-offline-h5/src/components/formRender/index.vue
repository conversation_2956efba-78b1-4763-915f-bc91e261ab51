<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { request, CommonRes } from "@ys/tools";
import { getAnswerByIdApi } from "@/api";
import formTitle from "./title.vue";
import formInput from "./input.vue";
import formTextarea from "./textarea.vue";
import formRadio from "./radio.vue";
import formCheckbox from "./checkbox.vue";
import formRate from "./rate.vue";
import formNumber from "./number.vue";

const { commonApi } = request;
interface CourseInfo {
  formName: string;
  formInfo: string;
  formTemplateInfoId: string;
}
const props = defineProps<{
  formId: string;
  formInfoId?: string;
  answerId?: string;
}>();

const open = ref(true);
const formName = ref("");
const columns = ref<
  {
    prop: string;
    type: string;
    label: string;
    column: any[];
  }[]
>([]);

const formValue = ref({});

async function getFormInfo() {
  if (!props.formId) return;

  const result = await commonApi.formGetOneData<CommonRes<CourseInfo>>({
    formId: props.formId,
  });
  formName.value = result.data.data.formName;
  const optionJson = JSON.parse(result.data.data.formInfo);
  const column = optionJson.column || [];
  const group = optionJson.group || [];
  columns.value = [...column, ...group];
}

async function getAnswer() {
  const result = await getAnswerByIdApi<CommonRes<any>>({
    id: props.answerId,
    templateUsedId: props.formInfoId,
  });
  if (result.data.code === 0) {
    formValue.value = result.data.data[0];
  }
}

onMounted(async () => {
  if (props.formInfoId && props.answerId) {
    await getAnswer();
  }
  await getFormInfo();
});

function onFormValid() {
  return formValue.value;
}

defineExpose({
  onFormValid,
});
</script>

<template>
  <div class="form-render">
    <div class="form-render-head" @click="open = !open">
      <div class="title text-overflow">{{ formName }}</div>
      <div class="icon">
        <van-icon v-if="open" name="arrow-up" size="20" />
        <van-icon v-else name="arrow-down" size="20" />
      </div>
    </div>
    <div v-show="open" class="form-render-body">
      <template v-for="column in columns">
        <template v-if="!column.column">
          <formTitle v-if="column.type === 'title'" :column="column" />
          <formInput
            v-if="column.type === 'input'"
            :column="column"
            :formValue="formValue"
          />
          <formTextarea
            v-if="column.type === 'textarea'"
            :column="column"
            :formValue="formValue"
          />
          <formRadio
            v-if="column.type === 'radio'"
            :column="column"
            :formValue="formValue"
          />
          <formCheckbox
            v-if="column.type === 'checkbox'"
            :column="column"
            :formValue="formValue"
          />
          <formRate
            v-if="column.type === 'rate'"
            :column="column"
            :formValue="formValue"
          />
          <formNumber
            v-if="column.type === 'number'"
            :column="column"
            :formValue="formValue"
          />
        </template>
        <template v-else>
          <div class="form-group-title">{{ column.label }}</div>
          <div v-for="(sunColumn, index) in column.column">
            <formTitle v-if="sunColumn.type === 'title'" :column="sunColumn" />
            <formInput
              v-if="sunColumn.type === 'input'"
              :column="sunColumn"
              :formValue="formValue"
            />
            <formTextarea
              v-if="sunColumn.type === 'textarea'"
              :column="sunColumn"
              :formValue="formValue"
            />
            <formRadio
              v-if="sunColumn.type === 'radio'"
              :column="sunColumn"
              :formValue="formValue"
            />
            <formCheckbox
              v-if="sunColumn.type === 'checkbox'"
              :column="sunColumn"
              :formValue="formValue"
            />
            <formRate
              v-if="sunColumn.type === 'rate'"
              :column="sunColumn"
              :formValue="formValue"
            />
            <formNumber
              v-if="sunColumn.type === 'number'"
              :column="sunColumn"
              :formValue="formValue"
            />
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.form-render {
  background: #fff;
  border-radius: 8px;
}
.form-render-head {
  height: 52px;

  padding: 0 16px;
  display: flex;
  align-items: center;
  .title {
    flex: 1;
    font-weight: 500;
    font-size: 16px;
    color: #000000;
  }
}
.form-render-body {
  border-top: 1px solid #e5e5e5;
  padding: 16px;
  > div + div {
    margin-top: 20px;
  }
}
.form-group-title {
  margin: 10px 0 10px 0;
  height: 40px;
  background: rgba(74, 134, 246, 0.16);
  border-radius: 8px;
  text-align: center;
  line-height: 40px;
  color: #4465f3;
  font-size: 16px;
  font-weight: 700;
}
</style>
