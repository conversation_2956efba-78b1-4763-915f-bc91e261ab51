import { ref, onMounted } from "vue";
import { getTaskListApi } from "@/api";
import { CommonRes, common } from "@ys/tools";
const { getSessionUser } = common;
const user = getSessionUser();
type TASK = {
  lectureBaseTaskId: string;
  taskName: string;
  remark: string;
  startDate: string;
  endDate: string;
  recordCount: number;
  uploadRequire: 1 | 2;
  evaluateRequire: 1 | 2;
  useTable: 1 | 2;
  patrolOrgRequire: 1 | 2;
  patrolOrgShow: 1 | 2;
  patrolUserRequire: 1 | 2;
  patrolUserShow: 1 | 2;
  formInfoId: "";
  formInfoName: "";
  formTemplateInfoId: "";
};

export function useList() {
  const taskList = ref<TASK[]>([]);
  const pagination = ref({
    currPage: 1,
    pageSize: 10,
    status: 1,
    userId: user?.id,
  });
  const total = ref(0);
  const maxPage = ref(1);

  async function getList() {
    const result = await getTaskListApi<CommonRes<any>>(pagination.value);
    if (result.data.code === 0) {
      if (pagination.value.currPage === 1) {
        taskList.value = result.data.data.records;
      } else {
        taskList.value = [...taskList.value, ...result.data.data.records];
      }
      total.value = Number(result.data.data.total);
      maxPage.value = Math.ceil(total.value / pagination.value.pageSize);
    }
  }
  async function onChangeTab(tab: 1 | 2) {
    if (pagination.value.status === tab) return;
    pagination.value.currPage = 1;
    pagination.value.status = tab;
    getList();
  }

  async function onScrollBottom() {
    if (pagination.value.currPage < maxPage.value) {
      pagination.value.currPage++;
      getList();
    }
  }

  onMounted(() => {
    getList();
  });

  return {
    taskList,
    onChangeTab,
    onScrollBottom,
    pagination,
  };
}
