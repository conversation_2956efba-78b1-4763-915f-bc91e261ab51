<template>
  <div class="task-list">
    <div class="nav">
      <div
        class="nav-item"
        :class="{ active: pagination.status == 1 }"
        @click="onChangeTab(1)"
      >
        <span>进行中</span>
        <i></i>
      </div>
      <div
        class="nav-item"
        :class="{ active: pagination.status == 2 }"
        @click="onChangeTab(2)"
      >
        <span>已结束</span>
        <i></i>
      </div>
    </div>
    <ul class="list" @scroll="onScroll">
      <li
        @click="router.push(`/record/${task.lectureBaseTaskId}`)"
        v-for="task in taskList"
      >
        <h3 class="title">
          <img src="./../../assets/images/icon1.png" alt="" />
          <div class="text-overflow">{{ task.taskName }}</div>
        </h3>
        <div class="record-info">
          <span class="text1">提交记录</span>
          <span class="text1">{{ task.recordCount }}</span>
        </div>
        <div class="date">
          <span class="text1">{{
            dayjs(task.startDate).format("YYYY-MM-DD")
          }}</span>
          <span class="text1">~</span>
          <span class="text1">{{
            dayjs(task.endDate).format("YYYY-MM-DD")
          }}</span>
        </div>
      </li>
      <empt v-if="taskList.length === 0" />
    </ul>
  </div>
</template>

<script setup lang="ts">
import empt from "@/components/empt/index.vue";
import { useList } from "./useList";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
const router = useRouter();
const { taskList, onScrollBottom, onChangeTab, pagination } = useList();
const onScroll = (e: any) => {
  if (
    e.currentTarget.scrollTop + e.currentTarget.clientHeight >=
    e.currentTarget.scrollHeight
  ) {
    onScrollBottom();
  }
};
</script>

<style lang="scss" scoped>
ul,
li {
  margin: 0;
  padding: 0;
  list-style: none;
}
.task-list {
  width: 100vw;
  height: 100vh;
  background: #f2f2f6;
  color: #1a1a1a;
  .nav {
    display: flex;
    align-items: center;
    width: 100%;
    height: 44px;
    background: #fff;
    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      height: 100%;
      span {
        font-size: 15px;
        color: #1a1a1a;
      }
      i {
        margin: 4px 0;
        width: 20px;
        height: 3px;

        border-radius: 2px;
      }
      &.active i {
        background: #007aff;
      }
    }
  }
  .list {
    position: relative;
    width: 100%;
    height: calc(100vh - 44px);
    padding: 12px;
    box-sizing: border-box;
    overflow-y: auto;
    li {
      margin-bottom: 12px;
      width: 100%;
      padding: 18px 16px;
      box-sizing: border-box;
      border-radius: 8px;
      background: #fff;
      .title {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        line-height: 22px;
        display: flex;
        align-items: center;
        img {
          margin-right: 6px;
          width: 18px;
        }
        > div {
          flex: 1;
          width: 0;
        }
      }
      .record-info {
        margin-top: 10px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.7);
        font-size: 14px;
        .text1 {
          margin-right: 8px;
        }
      }
      .date {
        margin-top: 10px;
        display: flex;
        align-items: center;
        color: #808080;
        font-size: 14px;
        .icon1 {
          font-size: 18px;
        }
        .text1 {
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
