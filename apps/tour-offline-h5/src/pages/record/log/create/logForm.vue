<script lang="ts" setup>
import { TASK, Record } from "@/entity";
import { getCommentApi, selectOrgByNameApi } from "@/api";
import { ref, onMounted, computed } from "vue";
import { CommonRes, common } from "@ys/tools";
import { ysShowPreviewH5 } from "@ys/ui";
import { showLoadingToast, closeToast, showFailToast } from "vant";
import empt from "@/components/empt/index.vue";
const { upload, fileIsImage, isCdn } = common;
const props = defineProps<{
  task: TASK;
}>();
defineExpose({
  onExpose,
  onInitValue,
});

const nameRequired = computed(() => {
  return props.task?.patrolUserRequire === 1;
});
const showName = computed(() => {
  return props.task?.patrolUserShow === 1;
});

const orgRequired = computed(() => {
  return props.task?.patrolOrgRequire === 1;
});
const showOrg = computed(() => {
  return props.task?.patrolOrgShow === 1;
});
const remarkRequired = computed(() => {
  return props.task?.evaluateRequire === 1;
});
const uploadRequired = computed(() => {
  return props.task?.uploadRequire === 1;
});

type Org = { id: string; displayName: string };
const show = ref(false);

const name = ref("");
const org = ref<{ id: string; label: string; list: Org[]; show: boolean }>({
  id: "",
  label: "",
  list: [],
  show: false,
});
const remark = ref("");
const attrs = ref<any>([]);

const commentList = ref<{ remark: string; lectureCommentId: string }[]>([]);
async function getComment() {
  const result = await getCommentApi<CommonRes<any>>({
    currPage: 1,
    pageSize: 999,
  });
  if (result.data.code === 0) {
    commentList.value = result.data.data.records;
  }
}

onMounted(() => {
  getComment();
});

async function selectOrgByName(orgName: string) {
  if (!orgName) {
    org.value.show = false;
    return;
  }
  const result = await selectOrgByNameApi<
    CommonRes<{ id: string; displayName: string }[]>
  >({ orgName });
  if (result.data.code === 0) {
    org.value.list = result.data.data;
    org.value.show = true;
  }
}

// ======================
const fileRef = ref();
const beforeUpload = (file: any) => {
  const isImage = file.type.indexOf("image/") !== -1;
  const isVideo = file.type.indexOf("video/") !== -1;
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M && isVideo) {
    showFailToast({
      icon: "fail",
      message: "视频文件大小小于100M",
    });
  }
  if (!(isImage || isVideo)) {
    showFailToast({
      icon: "fail",
      message: "上传正确的格式",
    });
  }
  return (isImage || isVideo) && isLt100M;
};
async function onFileChange(e: any) {
  if (!beforeUpload(e.target.files[0])) {
    e.target.value = "";
    return;
  }

  showLoadingToast({
    message: "上传中...",
    forbidClick: true,
    duration: 0,
  });
  const result = await upload(e);
  closeToast(true);

  const attr = {
    smallPath: result.file.smallPath || result.file.screenUrl,
    filePath: result.filepath,
    name: result.filename,
    type: fileIsImage(result.filepath) ? 1 : 2,
  };
  attrs.value.push(attr);
}
function ondel(index: number) {
  attrs.value.splice(index, 1);
}

function onExpose() {
  if (showName.value && nameRequired.value && !name.value) {
    // 判断姓名
    showFailToast({
      icon: "fail",
      message: "请输入上课人",
    });
    return false;
  }
  if (showOrg.value && orgRequired.value && !org.value.id) {
    // 判断机构
    showFailToast({
      icon: "fail",
      message: "请输入机构",
    });
    return false;
  }
  if (remarkRequired.value && !remark.value) {
    // 判断评价
    showFailToast({
      icon: "fail",
      message: "请输入评价",
    });
    return false;
  }
  if (uploadRequired.value && attrs.value.length === 0) {
    showFailToast({
      icon: "fail",
      message: "请上传图片或视频",
    });
    return false;
  }
  const pictureCount = attrs.value.reduce((pre: number, cur: any) => {
    if (cur.type === 1) {
      return pre + 1;
    }
    return pre;
  }, 0);
  return {
    recoreUserName: name.value,
    recordOrgName: org.value.label,
    recordOrgId: org.value.id,
    attrList: attrs.value,
    lectureComment: remark.value,
    pictureCount,
    videoCount: attrs.value.length - pictureCount,
    attrTotal: attrs.value.length,
  };
}
function onInitValue(data: Record) {
  name.value = data.recoreUserName;
  org.value.id = data.recordOrgId;
  org.value.label = data.recordOrgName;
  remark.value = data.lectureComment;
  attrs.value = data.attrList || [];
}
</script>

<template>
  <div class="log-form">
    <div class="head">提交记录</div>
    <div class="body">
      <div class="form-item form-input" v-if="showName">
        <div class="label" :class="{ required: nameRequired }">上课人</div>
        <input
          type="text"
          v-model="name"
          :maxLength="50"
          placeholder="请输入"
        />
      </div>
      <div class="form-item form-input" v-if="showOrg">
        <div class="label" :class="{ required: orgRequired }">机构</div>
        <input
          type="text"
          @input="
            (e: any) => {
              org.id = '';
              selectOrgByName(e.target.value);
            }
          "
          v-model="org.label"
          placeholder="请输入"
        />
        <div class="orgList" v-if="org.show && org.list.length > 0">
          <div
            @click="
              () => {
                org.id = item.id;
                org.label = item.displayName;
                org.show = false;
              }
            "
            class="org-item text-overflow"
            v-for="item in org.list"
          >
            {{ item.displayName }}
          </div>
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-mark">
          <div class="label" :class="{ required: remarkRequired }">评价</div>
          <div @click="show = true" class="quck-reply">
            <img src="@/assets/images/icon3.png" alt="" />
            快捷评语
          </div>
        </div>
        <textarea
          v-model="remark"
          :autosize="false"
          :rows="3"
          placeholder="请输入"
          :maxLength="200"
        ></textarea>
      </div>
      <div class="form-item upload">
        <div class="label">上传(图片或视频)</div>
        <div class="upload-box">
          <div
            v-for="(attr, index) in attrs"
            :key="index"
            @click="ysShowPreviewH5(attr.filePath, attr.name)"
            class="upload-box-item"
          >
            <van-icon
              @click.stop="ondel(index)"
              class="icon"
              name="clear"
              size="30"
            />
            <van-icon
              v-if="attr.type === 2"
              class="videoIcon"
              name="play-circle-o"
              size="40"
            />
            <img :src="isCdn(attr.smallPath)" alt="" />
          </div>
          <div
            v-if="attrs.length < 9"
            class="upload-box-item trigger"
            @click="fileRef.click()"
          >
            <input
              ref="fileRef"
              @change="onFileChange"
              type="file"
              accept="image/*,video/*"
            />
            <van-icon name="plus" size="40" color="#808080" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <van-action-sheet v-model:show="show" title="快捷评语">
    <div class="comment">
      <div
        class="comment-item text-overflow"
        @click="
          () => {
            remark += comment.remark;
            show = false;
          }
        "
        v-for="comment in commentList"
        :key="comment.lectureCommentId"
      >
        {{ comment.remark }}
      </div>
      <empt v-if="commentList.length === 0" />
    </div>
  </van-action-sheet>
</template>

<style lang="scss" scoped>
.log-form {
  background: #fff;
  border-radius: 8px;
}
.head {
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  font-weight: 500;
  font-size: 16px;
  color: #000000;
}

.body {
  padding: 0 16px 16px;
}

.form-item {
  position: relative;
  padding: 16px 0;
  border-bottom: 1px solid #e5e5e5;
  &.form-input {
    display: flex;
    align-items: center;
  }
  .label {
    font-size: 16px;
    color: #4d4d4d;
    position: relative;
    &.required::before {
      content: "*";
      color: #ff3141;
      position: absolute;
      right: -8px;
      top: 4px;
    }
  }
  input {
    flex: 1;
    border: none;
    outline: none;
    text-align: right;
  }
  textarea {
    width: 100%;
    border: none;
    outline: none;
  }
  .form-item-mark {
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .quck-reply {
      background: #f8f8f8;
      padding: 6px 8px;
      border-radius: 50px;
      display: flex;
      align-items: center;
      img {
        margin-right: 2px;
        width: 18px;
      }
    }
  }
  &.upload {
    padding-bottom: 0;
    border: none;
  }
  .upload-box {
    margin-top: 18px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 8px;
    grid-row-gap: 8px;

    .upload-box-item {
      position: relative;
      aspect-ratio: 1;
      background: #f5f5f5;
      border-radius: 4px;
      .icon {
        z-index: 9;
        position: absolute;
        right: -12px;
        top: -12px;
        color: rgba(0, 5, 32, 0.6);
      }
      .videoIcon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
      }
      img {
        border-radius: 4px;
        width: 100%;
        height: 100%;
      }
    }
    .trigger {
      display: flex;
      align-items: center;
      justify-content: center;
      input {
        display: none;
      }
    }
  }
}

.comment {
  position: relative;
  height: 400px;
  border-top: 1px solid #ededed;
  .comment-item {
    padding: 16px;
    border-bottom: 1px solid #ededed;
  }
}

.orgList {
  z-index: 9;
  position: absolute;
  right: 0;
  top: 60px;
  background: #fff;
  box-shadow: 0px 0px 30px 0px rgba(1, 1, 1, 0.2);
  border-radius: 4px;
  width: 270px;
  height: 160px;
  overflow-y: auto;
  .org-item {
    line-height: 48px;
    padding: 0 16px;
  }
}
</style>
