<script lang="ts" setup>
import { ref } from "vue";
import { Record } from "@/entity";
import { ysIcon, ysShowPreviewH5 } from "@ys/ui";
import { common } from "@ys/tools";
const { isCdn } = common;
defineProps<{
  record: Record;
}>();
</script>

<template>
  <div class="log-form-render">
    <div class="title">记录详情</div>
    <div class="record-info">
      <ysIcon
        v-if="record?.recoreUserName"
        class="icon1"
        style="font-size: 20px"
        type="iconyonghu11"
      />
      <div v-if="record?.recoreUserName" class="name text-overflow">
        {{ record?.recoreUserName }}
      </div>
      <div v-if="record?.recordOrgName" class="org text-overflow">
        {{ record?.recordOrgName }}
      </div>
    </div>
    <div class="record-remark">
      {{ record?.lectureComment }}
    </div>
    <div class="record-attr" v-if="record?.attrList">
      <div
        v-for="(attr, index) in record?.attrList"
        @click="ysShowPreviewH5(attr.filePath, attr.name)"
        :key="index"
        class="upload-box-item"
      >
        <van-icon
          v-if="attr.type === 2"
          class="videoIcon"
          name="play-circle-o"
          size="40"
        />
        <img :src="isCdn(attr.smallPath)" alt="" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.log-form-render {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}
.title {
  position: relative;
  padding-left: 8px;
  font-weight: 500;
  font-size: 16px;
  color: #000000;
  &::before {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    content: "";
    width: 4px;
    height: 12px;
    background: #007aff;
    border-radius: 4px;
  }
}
.record-info {
  display: flex;
  align-items: center;
  margin-top: 12px;
  font-weight: 500;
  font-size: 16px;
  color: #1a1a1a;
  .name {
    max-width: 100px;
    margin: 0 4px;
    font-size: 16px;
  }
  .org {
    font-size: 16px;
    flex: 1;
  }
}

.record-remark {
  word-break: break-all;
  margin-top: 12px;
  font-weight: 400;
  font-size: 16px;
  color: #4d4d4d;
}

.record-attr {
  margin-top: 18px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 8px;
  grid-row-gap: 8px;

  .upload-box-item {
    position: relative;
    aspect-ratio: 1;
    background: #f5f5f5;
    border-radius: 4px;
    .icon {
      z-index: 9;
      position: absolute;
      right: -12px;
      top: -12px;
      color: rgba(0, 5, 32, 0.6);
    }
    .videoIcon {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
    }
    img {
      border-radius: 4px;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
