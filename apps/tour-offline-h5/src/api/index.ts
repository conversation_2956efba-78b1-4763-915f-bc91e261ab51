import { request } from "@ys/tools";
const { instance, sd_url } = request;

export function selectOrgByNameApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/eventNew/selectOrgDownByName`,
    params,
  });
}
export function formsaveAnswerApi<T>(id: string, data: any) {
  return instance<T>({
    url: `${sd_url}/patrol/saveAnswerForRepeat?templateUsedId=${id}`,
    data,
    method: "post",
  });
}
export const getAnswerByIdApi = <T>(params: any) => {
  return instance<T>({
    url: "/sd-api/patrol/getAnswerById",
    params,
  });
};

export function getTaskDetailApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/base/get.do`,
    params,
  });
}

export function getTaskListApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/mobile/task/page.do`,
    params,
  });
}
export function getRecordListApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/mobile/record/page.do`,
    params,
  });
}
export function getRecordApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/mobile/record/get.do`,
    params,
  });
}
export function delRecordApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/mobile/record/delete.do`,
    params,
  });
}
export function editRecordApi<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/mobile/record/edit.do`,
    data,
    method: "post",
  });
}

export function getCommentApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/comment/page.do`,
    params,
  });
}
export function getLocaltionListApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/localtion/list.do`,
    params,
  });
}

export const getpublicconfig = <T>(params: any, tenantId: any) => {
  return instance<T>({
    url: `${sd_url}/sign/getpublicconfig`,
    params,
    headers: {
      tenantId,
    },
  });
};
