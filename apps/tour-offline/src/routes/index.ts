import { createRouter, createWebHashHistory } from "vue-router";
const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: "/",
      component: () => import("./../pages/layout.vue"),
      children: [
        {
          path: "/task",
          component: () => import("./../pages/task/index.vue"),
        },
        {
          path: "/comments",
          component: () => import("./../pages/setting/comments/index.vue"),
        },
        {
          path: "/system",
          component: () => import("./../pages/setting/system/index.vue"),
        },
      ],
    },
    {
      path: "/task/:id",
      component: () => import("./../pages/task/detail/index.vue"),
    },
  ],
});

router.beforeEach((to, from, next) => {
  if (from.query.bureauId && !to.query.bureauId) {
    let query = to.query;
    query.bureauId = from.query.bureauId;
    query.jd = from.query.jd;
    next({
      path: to.path,
      query: query,
    });
  }
  next();
});
export default router;
