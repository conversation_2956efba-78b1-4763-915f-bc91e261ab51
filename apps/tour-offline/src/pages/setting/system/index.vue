<template>
  <section class="comcomtent">
    <div class="title">系统设置</div>
    <div class="form">
      <div class="form-item">
        <span class="label">
          <i>*</i>
          <span>系统名称:</span>
        </span>
        <a-input
          style="width: 360px; margin-left: 8px"
          v-model:value="name"
          placeholder="请输入"
          show-count
          :maxlength="20"
        />
      </div>
      <div class="form-item current">
        <span class="label">
          <span>系统Logo:</span>
        </span>
        <div class="logo-box">
          <div class="logo" @click="handleUploadImg">
            <img :src="isCdn(logo)" />
          </div>
          <p class="tips">建议尺寸32px*32px</p>
          <a-button style="margin-top: 24px" type="primary" @click="onSave"
            >更新系统信息</a-button
          >
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { message } from "ant-design-vue";
import { common, CommonRes } from "@ys/tools";
import { ref, watchEffect } from "vue";
import { updateSystemApi } from "@/api";
import { system, getSystem } from "./system";
const { isCdn, upload, fileIsImage } = common;

const name = ref();
const logo = ref();

watchEffect(() => {
  name.value = system.value.systemName;
  logo.value = system.value.systemPic;
});

function handleUploadImg() {
  const input = document.createElement("input");
  input.type = "file";
  input.onchange = handleFile;
  document.body.appendChild(input);
  input.click();
  document.body.removeChild(input);
}

async function handleFile(e: any) {
  let file = e.target.files[0];
  if (!fileIsImage(file.name)) {
    message.error("请上传图片格式");
    return;
  }
  let res = await upload(e);
  logo.value = res.filepath;
}

const onSave = async () => {
  const result = await updateSystemApi<CommonRes<string>>({
    systemName: name.value,
    systemPic: logo.value,
  });
  if (result.data.code === 0) {
    message.success("保存成功");
    getSystem();
  }
};
</script>

<style lang="scss" scoped>
.comcomtent {
  width: 100%;
  padding: 16px 24px;
  box-sizing: border-box;
  .title {
    color: #262626;
    line-height: 22px;
  }
  .form {
    margin-top: 16px;
    width: 100%;
    padding: 16px 24px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 4px;
    .form-item {
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      &.current {
        align-items: flex-start;
      }
      .label {
        display: flex;
        justify-content: flex-end;
        width: 72px;
        i {
          font-style: normal;
          color: #f53f3f;
        }
        span {
          margin-left: 4px;
        }
      }
      .logo-box {
        margin-left: 8px;
        .logo {
          width: 64px;
          height: 64px;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .tips {
          margin-top: 8px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
