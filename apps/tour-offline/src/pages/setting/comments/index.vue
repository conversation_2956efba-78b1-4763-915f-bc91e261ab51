<template>
  <section class="comcomtent">
    <div class="title">快捷评语</div>
    <div class="content">
      <div class="content-head">
        <span>评语列表</span>
        <a-button type="primary" @click="onAddComment">
          <template #icon>
            <PlusOutlined />
          </template>
          添加
        </a-button>
      </div>
      <div class="table">
        <div class="table-head">
          <div class="table-head-item sort">排序</div>
          <div class="table-head-item index">序号</div>
          <div class="table-head-item comment">评语</div>
          <div class="table-head-item handler">操作</div>
        </div>
        <draggable
          class="dragOther"
          v-model="list"
          item-key="id"
          forceFallback="true"
          animation="300"
          handle=".sort"
          @end="onSortComment"
        >
          <template #item="{ element }">
            <div class="table-body">
              <div class="table-body-item sort">
                <ysIcon class="icon-drag" type="icontuozhuai1" />
              </div>
              <div class="table-body-item index">{{ element.index + 1 }}</div>
              <div
                class="table-body-item comment text-overflow"
                :title="element.remark"
              >
                {{ element.remark }}
              </div>
              <div class="table-body-item handler">
                <a-space :size="16">
                  <span class="handle-item" @click="onDel(element)">删除</span>
                  <span class="handle-item" @click="onEdit(element)">编辑</span>
                  <a-dropdown>
                    <ysIcon class="icon-more" type="icongengduo11" />
                    <template #overlay>
                      <a-menu>
                        <a-menu-item v-if="element.index != 0">
                          <a
                            @click="onSortSimpleComment('up', element.index)"
                            href="javascript:;"
                            >上移</a
                          >
                        </a-menu-item>
                        <a-menu-item v-if="element.index != list.length - 1">
                          <a
                            @click="onSortSimpleComment('down', element.index)"
                            href="javascript:;"
                            >下移</a
                          >
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </div>
            </div>
          </template>
        </draggable>
      </div>
      <ysPagination
        :pageNo="pagination.currPage"
        :pageSize="pagination.pageSize"
        :total="total"
        @change="pageChange"
      />
    </div>
  </section>

  <addModal
    :currentComment="currentComment"
    :visible="visible"
    @cancel="onAddCancel"
    @ok="getList"
  />
</template>

<script setup lang="ts">
import addModal from "./addModal.vue";
import draggable from "vuedraggable";
import { PlusOutlined } from "@ant-design/icons-vue";
import { ysIcon, ysPagination } from "@ys/ui";
import { useComment } from "./useComment";
const {
  currentComment,
  list,
  pagination,
  total,
  getList,
  pageChange,
  onAddComment,
  onAddCancel,
  onEdit,
  onDel,
  onSortComment,
  onSortSimpleComment,
  visible,
} = useComment();
</script>

<style lang="scss" scoped>
.comcomtent {
  width: 100%;
  padding: 16px 24px;
  box-sizing: border-box;
  .title {
    color: #262626;
    line-height: 22px;
  }
  .content {
    margin-top: 16px;
    width: 100%;
    padding: 16px 24px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 4px;
    .content-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }
    .table {
      margin-top: 16px;
      width: 100%;
      .table-head {
        display: flex;
        align-items: center;
        width: 100%;
        height: 56px;
        background: #fafafa;
        .table-head-item {
          display: flex;
          align-items: center;
          height: 100%;
          padding: 0 16px;
          box-sizing: border-box;
          color: #262626;
          &.sort {
            width: 60px;
          }
          &.index {
            flex: 1;
          }
          &.comment {
            flex: 2;
          }
          &.handler {
            width: 140px;
          }
        }
      }
      .table-body {
        display: flex;
        align-items: center;
        width: 100%;
        height: 56px;
        background: #fff;
        box-shadow: inset 0px -1px 0px 0px #f0f0f0;
        .table-body-item {
          padding: 0 16px;
          box-sizing: border-box;
          color: #262626;
          &.sort {
            width: 60px;
          }
          &.index {
            flex: 1;
          }
          &.comment {
            flex: 2;
            width: 0;
          }
          &.handler {
            width: 140px;
          }
          .icon-more {
            margin-top: 4px;
            font-size: 18px;
            color: #007aff;
          }
          .icon-drag {
            font-size: 18px;
          }
          .handle-item {
            color: #007aff;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
