import { ref, onMounted } from "vue";
import {
  listExpertUserByName,
  addTaskScheduleApi,
  delTaskScheduleApi,
  getTaskScheduleApi,
} from "@/api";
import { CommonRes } from "@ys/tools";
import { ysShowConfirmModal } from "@ys/ui";
import { message } from "ant-design-vue";
interface User {
  lectureBaseTaskId: string;
  lectureTaskScheduleId: string;
  userName: string;
  mobile: string;
  orgId: string;
  orgName: string;
}
type UserResponse = CommonRes<{
  records: User[];
  total: string;
}>;

export function useUser(taskId: string) {
  const userOptions = ref([]);
  const list = ref<User[]>([]);
  const pagination = ref({
    currPage: 1,
    pageSize: 10,
    name: "",
  });
  const total = ref(0);
  async function getList() {
    const result = await getTaskScheduleApi<UserResponse>({
      ...pagination.value,
      taskId,
    });
    if (result.data.code === 0) {
      list.value = result.data.data.records;
      total.value = Number(result.data.data.total);
    }
  }

  function pageChange(pageNo: number, pageSize: number) {
    pagination.value.currPage = pageNo;
    pagination.value.pageSize = pageSize;
    getList();
  }
  function onNameSearch() {
    pagination.value.currPage = 1;
    getList();
  }
  function onDel(scheduleId: string) {
    ysShowConfirmModal("确认移除?").then(async (_) => {
      const result = await delTaskScheduleApi<CommonRes<null>>({ scheduleId });
      if (result.data.code === 0) {
        message.success("移除成功");
        pagination.value.currPage = 1;
        getList();
      }
    });
  }

  onMounted(() => {
    getList();
  });

  async function userChange(userId: string) {
    const result = await addTaskScheduleApi<CommonRes<string>>({
      taskId,
      userId,
    });
    if (result.data.code === 0) {
      message.success("添加成功");
      getList();
    }
  }
  async function userSearch(userName: string) {
    if (!userName) return;
    const result = await listExpertUserByName<CommonRes<any>>({
      name: userName,
    });
    userOptions.value = result.data.data.map((teacher: any) => ({
      ...teacher,
      label: teacher.userName,
      value: teacher.id,
    }));
  }
  return {
    userOptions,
    userSearch,
    userChange,
    pagination,
    list,
    total,
    pageChange,
    onNameSearch,
    onDel,
  };
}

export const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
  },
  {
    title: "姓名",
    dataIndex: "userName",
    key: "userName",
    ellipsis: true,
  },
  {
    title: "机构",
    dataIndex: "orgName",
    key: "orgName",
    ellipsis: true,
  },
  {
    title: "手机号",
    dataIndex: "mobile",
    key: "mobile",
  },
  {
    title: "提交记录",
    dataIndex: "recordCount",
    key: "recordCount",
  },
  {
    title: "操作",
    dataIndex: "handler",
    key: "handler",
    width: 60,
  },
];
