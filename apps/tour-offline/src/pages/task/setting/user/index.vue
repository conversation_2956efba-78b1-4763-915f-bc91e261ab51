<template>
  <div class="arrangement">
    <div class="add-person">
      <span>添加人员: </span>
      <a-select
        show-search
        placeholder="请输入"
        style="width: 440px"
        :default-active-first-option="false"
        :show-arrow="false"
        :filter-option="false"
        :not-found-content="null"
        :options="userOptions"
        @change="userChange"
        @search="userSearch"
      >
        <template #option="{ value, label, phone, displayName }">
          <div class="option-item">
            <div class="name text-overflow">{{ label }}</div>
            <div class="phone">{{ encryPhone(phone) }}</div>
            <div class="org text-overflow">{{ displayName }}</div>
            <ysIcon class="icon" type="iconshanchu" />
          </div>
        </template>
      </a-select>
    </div>

    <div class="search">
      <span>人员列表</span>
      <a-input-search
        v-model:value="pagination.name"
        placeholder="请输入姓名查询"
        style="width: 240px"
        @search="onNameSearch"
      />
    </div>

    <a-table
      style="margin-top: 16px"
      :dataSource="list"
      :columns="columns"
      :pagination="false"
      :rowKey="(record: any) => record.lectureTaskScheduleId"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'index'">
          {{ index + 1 }}
        </template>
        <template v-if="column.key === 'recordCount'">
          {{ record.recordCount || 0 }}
        </template>
        <template v-if="column.key === 'mobile'">
          {{ encryPhone(record.mobile) }}
        </template>
        <template v-if="column.dataIndex == 'handler'">
          <a-space>
            <div
              @click="() => onDel(record.lectureTaskScheduleId)"
              class="handler"
            >
              移除
            </div>
          </a-space>
        </template>
      </template>
    </a-table>
    <ysPagination
      :pageNo="pagination.currPage"
      :pageSize="pagination.pageSize"
      :total="total"
      @change="pageChange"
    />
  </div>
</template>

<script setup lang="ts">
import { useUser, columns } from "./useUser";
import { common } from "@ys/tools";
import { ysIcon, ysPagination } from "@ys/ui";
const props = defineProps<{
  taskId: string;
}>();
const { encryPhone } = common;
const {
  list,
  pagination,
  total,
  onNameSearch,
  onDel,
  pageChange,
  userChange,
  userOptions,
  userSearch,
} = useUser(props.taskId);
</script>

<style lang="scss" scoped>
.arrangement {
  margin-top: 20px;
  .search {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  .handler {
    cursor: pointer;
    color: #007aff;
  }
}

.option-item {
  height: 40px;
  display: flex;
  align-items: center;

  .name {
    width: 80px;
  }

  .phone {
    width: 100px;
    color: #8c8c8c;
    margin: 0 24px;
  }

  .org {
    flex: 1;
  }

  &:hover .icon {
    opacity: 1;
  }

  .icon {
    opacity: 0;
    display: inline-block;
    transform: rotate(45deg);
    color: #007aff;
    font-size: 20px;
  }
}
</style>
