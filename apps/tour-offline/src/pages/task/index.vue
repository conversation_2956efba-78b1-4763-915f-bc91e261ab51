<template>
  <section class="task">
    <div class="title">听课任务</div>
    <div class="check">
      <div class="check-item">
        <span>搜索: </span>
        <a-input
          style="width: 222px"
          v-model:value="pagination.name"
          placeholder="任务名称"
        />
      </div>
      <div class="check-item">
        <span>日期: </span>
        <a-date-picker
          style="width: 222px"
          v-model:value="pagination.date"
          valueFormat="YYYY-MM-DD"
        />
      </div>

      <div class="check-item">
        <a-button @click="onSearch" type="primary">查询</a-button>
        <a-button @click="onClear" style="margin-left: 8px">重置</a-button>
      </div>
    </div>

    <div class="task-list">
      <div class="title2">
        <span>任务列表</span>
        <a-button
          type="primary"
          @click="
            () => {
              task = undefined;
              addVisible = true;
            }
          "
        >
          <template #icon><PlusOutlined /></template>
          添加
        </a-button>
      </div>

      <a-table
        class="touroffTable"
        style="margin-top: 16px"
        :dataSource="list"
        :columns="columns"
        :pagination="false"
        :rowKey="(record: any) => record.lectureBaseTaskId"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ index + 1 }}
          </template>
          <template v-if="column.key === 'recordCount'">
            {{ record.recordCount || 0 }}
          </template>
          <template v-if="column.key === 'remark'">
            {{ record.remark || "--" }}
          </template>
          <template v-if="column.key === 'date'">
            {{ dayjs(record.startDate).format("YYYY-MM-DD") }}
            ~
            {{ dayjs(record.endDate).format("YYYY-MM-DD") }}
          </template>
          <template v-if="column.dataIndex == 'status'">
            <div class="status">
              <i
                class="dot"
                :class="[record.status == 1 ? 'open' : 'close']"
              ></i>
              <span>{{ record.status == 1 ? "开启" : "关闭" }} </span>
            </div>
          </template>
          <template v-if="column.dataIndex == 'handler'">
            <a-space>
              <div
                class="handler"
                @click="
                  () => {
                    task = record;
                    settingVisible = true;
                  }
                "
              >
                设置
              </div>
              <div
                class="handler"
                @click="router.push(`/task/${record.lectureBaseTaskId}`)"
              >
                详情
              </div>
              <a-dropdown>
                <ysIcon class="icon1 handler" type="icongengduo11" />
                <template #overlay>
                  <a-menu>
                    <a-menu-item>
                      <a
                        @click="
                          () =>
                            onChangeStatus(
                              record.status,
                              record.lectureBaseTaskId
                            )
                        "
                        href="javascript:;"
                        >{{ record.status == 1 ? "关闭" : "开启" }}</a
                      >
                    </a-menu-item>
                    <a-menu-item>
                      <a
                        @click="
                          () => {
                            task = record;
                            addVisible = true;
                          }
                        "
                        href="javascript:;"
                        >复制</a
                      >
                    </a-menu-item>
                    <a-menu-item>
                      <a
                        @click="
                          () => {
                            onDelTask(record.lectureBaseTaskId);
                          }
                        "
                        href="javascript:;"
                        >删除</a
                      >
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
      <ysPagination
        v-if="total > 0"
        :pageNo="pagination.currPage"
        :pageSize="pagination.pageSize"
        :total="total"
        @change="pageChange"
      />
    </div>
  </section>

  <addModal
    :task="task"
    :visible="addVisible"
    @onCancel="addVisible = false"
    @onOk="
      () => {
        addVisible = false;
        getList();
      }
    "
  />

  <settingDrawer
    :task="task"
    :visible="settingVisible"
    @close="
      () => {
        settingVisible = false;
        getList();
      }
    "
  />
</template>

<script setup lang="ts">
import { TASK } from "@/entity";
import { ysIcon, ysPagination } from "@ys/ui";
import dayjs from "dayjs";
import { PlusOutlined } from "@ant-design/icons-vue";
import addModal from "./addModal.vue";
import settingDrawer from "./setting/index.vue";
import { useRouter } from "vue-router";
import { ref } from "vue";
import { useTaskList, columns } from "./useTaskList";
const router = useRouter();
const {
  list,
  pagination,
  total,
  getList,
  onDelTask,
  pageChange,
  onSearch,
  onClear,
  onChangeStatus,
} = useTaskList();
const task = ref<TASK>();
const addVisible = ref(false);
const settingVisible = ref(false);
</script>

<style lang="scss" scoped>
.task {
  width: 100%;
  height: 100%;
  padding: 16px 24px;
  box-sizing: border-box;
  .title {
  }
  .check {
    display: flex;
    align-items: center;
    margin-top: 16px;
    width: 100%;
    padding: 24px 12px;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    .check-item {
      display: flex;
      align-items: center;
      &:last-child {
        margin-left: 24px;
      }
      span {
        margin-right: 12px;
        width: 96px;
        height: 22px;
        text-align: right;
      }
    }
  }
  .task-list {
    margin-top: 16px;
    width: 100%;
    padding: 16px 24px;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    .title2 {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .status {
      display: flex;
      align-items: center;
      .dot {
        width: 6px;
        height: 6px;

        border-radius: 50%;
        &.open {
          background: #17be6b;
        }
        &.close {
          background: #f53f3f;
        }
      }
      span {
        margin-left: 8px;
      }
    }
    .handler {
      cursor: pointer;
      color: #007aff;
    }
    .icon1 {
      margin-top: 3px;
      font-size: 18px;
    }
  }
}
</style>
<style>
.touroffTable .ant-table-thead > tr > th {
  color: #000;
}
</style>
