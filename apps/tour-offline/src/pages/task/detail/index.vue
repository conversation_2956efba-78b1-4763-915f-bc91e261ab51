<template>
  <main class="task-detail">
    <Header />
    <div class="title">
      <div class="title-container">
        <span>详情 - {{ task?.taskName }}</span>
        <div class="close">
          <ysIcon class="icon1" type="icon-close" @click="onBack" />
        </div>
      </div>
    </div>
    <div class="content">
      <div class="title2" style="margin-bottom: 16px">
        <span>详情列表</span>

        <a-input-group compact style="width: 330px">
          <a-select v-model:value="pagination.type" style="width: 120px">
            <a-select-option :value="0">全部</a-select-option>
            <a-select-option :value="1">提交人</a-select-option>
            <a-select-option :value="2">被记录机构</a-select-option>
            <a-select-option :value="3">被记录人</a-select-option>
          </a-select>
          <a-input
            v-model:value="pagination.keyword"
            @pressEnter="onSearch"
            placeholder="搜索"
            style="width: 210px"
          />
        </a-input-group>
      </div>

      <a-table
        :dataSource="list"
        :columns="columns"
        :pagination="false"
        :rowKey="(record: any) => record.lectureTaskRecordId"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ index + 1 }}
          </template>

          <template v-if="column.key === 'recoreUserName'">
            <div class="text-overflow" :title="record.recoreUserName">
              {{ record.recoreUserName || "--" }}
            </div>
          </template>
          <template v-if="column.key === 'recordOrgName'">
            <div class="text-overflow" :title="record.recordOrgName">
              {{ record.recordOrgName || "--" }}
            </div>
          </template>
          <template v-if="column.key === 'submitAddress'">
            <div class="text-overflow" :title="record.submitAddress">
              {{ record.submitAddress || "--" }}
            </div>
          </template>
          <template v-if="column.key === 'lectureComment'">
            <div class="text-overflow" :title="record.lectureComment">
              {{ record.lectureComment || "--" }}
            </div>
          </template>

          <template v-if="column.dataIndex == 'useTable'">
            <div class="table have" v-if="record.useTable === 1">有</div>
            <div class="table no" v-else>无</div>
          </template>
          <template v-if="column.dataIndex == 'handler'">
            <a-space>
              <div
                class="handler"
                @click="onOpenDrawer(record.lectureTaskRecordId)"
              >
                详情
              </div>
            </a-space>
          </template>
        </template>
      </a-table>

      <ysPagination
        hideOnSinglePage
        :pageNo="pagination.currPage"
        :pageSize="pagination.pageSize"
        :total="total"
        @change="pageChange"
      />
    </div>
  </main>

  <Drawer
    :visible="drawerVisible"
    @close="drawerVisible = false"
    :recordId="lectureTaskRecordId"
  />
</template>

<script setup lang="ts">
import Header from "@/components/header.vue";
import Drawer from "./drawer/index.vue";
import { ysIcon, ysPagination } from "@ys/ui";
import { useRouter } from "vue-router";
import { ref } from "vue";
import { useRecordList } from "./useRecordList";
const { list, task, pagination, pageChange, total, onSearch } = useRecordList();
const router = useRouter();
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    width: 60,
  },
  {
    title: "被记录人",
    dataIndex: "recoreUserName",
    key: "recoreUserName",
    ellipsis: true,
  },
  {
    title: "被记录机构",
    dataIndex: "recordOrgName",
    key: "recordOrgName",
    ellipsis: true,
  },
  {
    title: "提交时间",
    dataIndex: "submitTime",
    key: "submitTime",
    width: 200,
    sorter: true,
  },
  {
    title: "提交人",
    dataIndex: "submitUserName",
    key: "submitUserName",
    ellipsis: true,
  },
  {
    title: "图片",
    dataIndex: "pictureCount",
    key: "pictureCount",
  },
  {
    title: "视频",
    dataIndex: "videoCount",
    key: "videoCount",
  },
  {
    title: "量表",
    dataIndex: "useTable",
    key: "useTable",
  },
  {
    title: "签到位置",
    dataIndex: "submitAddress",
    key: "submitAddress",

    ellipsis: true,
  },
  {
    title: "评语",
    dataIndex: "lectureComment",
    key: "lectureComment",

    ellipsis: true,
  },
  {
    title: "操作",
    dataIndex: "handler",
    key: "handler",
    width: 60,
  },
];

const drawerVisible = ref(false);
const lectureTaskRecordId = ref("");
const onBack = () => {
  router.back();
};

const onOpenDrawer = (recordId: string) => {
  lectureTaskRecordId.value = recordId;
  drawerVisible.value = true;
};
</script>

<style lang="scss" scoped>
.task-detail {
  width: 100%;
  height: 100vh;
  background: #f0f2f5;
  .content {
    margin: 24px auto;
    width: 1344px;
    padding: 16px 24px;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 4px;
    .title2 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }
  }
  .title {
    width: 100%;
    height: 56px;
    background: #ffffff;
    .title-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      width: 1344px;
      height: 100%;
      .close {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        background: #f0f0f0;
        cursor: pointer;
        .icon1 {
          font-size: 18px;
        }
      }
    }
  }
  .table {
    width: 29px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    box-sizing: border-box;
    border-radius: 4px 4px 4px 4px;

    &.have {
      border: 1px solid #7ac8ff;
      color: #007aff;
      background: #e6f2ff;
    }
    &.no {
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
      color: #262626;
    }
  }
  .handler {
    cursor: pointer;
    color: #007aff;
  }
}
</style>
