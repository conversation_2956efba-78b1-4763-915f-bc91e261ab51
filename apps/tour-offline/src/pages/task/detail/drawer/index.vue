<template>
  <a-drawer
    v-model:visible="props.visible"
    :width="720"
    class="detail-drawaer-class"
    title="详情"
    placement="right"
    :closable="false"
    @after-visible-change="afterVisibleChange"
    destroyOnClose
  >
    <template #extra>
      <ysIcon class="icon1" type="icon-close" @click="onClose" />
    </template>
    <div class="tabbar">
      <div
        class="tabbar-item"
        :class="[{ active: active == 1 }]"
        @click="onChangeTabbar(1)"
      >
        <span>基础信息</span>
        <i></i>
      </div>
      <div
        class="tabbar-item"
        :class="[{ active: active == 2 }]"
        @click="onChangeTabbar(2)"
      >
        <span>量表</span>
        <i></i>
      </div>
    </div>
    <div class="tabbar-body">
      <template v-if="active === 1">
        <BaseInfo :recordId="recordId" />
      </template>

      <template v-if="active === 2">
        <Scale :recordId="recordId" />
      </template>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import BaseInfo from "./baseinfo.vue";
import Scale from "./scale.vue";
import { ysIcon } from "@ys/ui";
import { ref } from "vue";

const props = defineProps<{
  visible: boolean;
  recordId: string;
}>();

const emit = defineEmits(["close"]);

const active = ref(1);

const onChangeTabbar = (value: number) => {
  active.value = value;
};

const onClose = () => {
  emit("close");
};

const afterVisibleChange = () => {
  active.value = 1;
};
</script>

<style lang="scss" scoped>
.tabbar {
  display: flex;
  align-items: center;
  width: 100%;
  height: 56px;
  padding: 0 24px;
  .tabbar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    margin-right: 32px;
    width: 56px;
    height: 56px;
    cursor: pointer;
    &.active {
      span {
        color: #007aff;
      }
      i {
        background: #007aff;
      }
    }
    i {
      margin-top: 14px;
      width: 100%;
      height: 3px;
      background: transparent;
    }
  }
}
.tabbar-body {
  flex: 1;
  height: 0;
  overflow-y: auto;
  padding: 0 12px;
}
</style>

<style lang="scss">
.detail-drawaer-class {
  .icon1 {
    font-size: 20px;
    color: #8c8c8c;
    cursor: pointer;
  }
  .ant-drawer-body {
    padding: 0;
    display: flex;
    flex-direction: column;
  }
}
</style>
