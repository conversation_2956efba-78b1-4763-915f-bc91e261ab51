<template>
  <div class="scale">
    <formDetail
      v-if="showTable"
      :formId="record?.formTemplateInfoId"
      :answerId="record?.formAnswer"
      :formInfoId="record?.formInfoId"
    />
    <img v-else src="./../../../../assets/images/empt.png" alt="" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { getRecordDetailApi } from "@/api";
import { Record } from "@/entity";
import { CommonRes } from "@ys/tools";
import formDetail from "./../../../../components/formDetail/index.vue";
const props = defineProps<{
  recordId: string;
}>();

const record = ref<Record>();

const showTable = computed(() => {
  if (!record.value) return false;
  return record.value.useTable == 1 && record.value.formTemplateInfoId;
});

onMounted(() => {
  getTaskRecord();
});
async function getTaskRecord() {
  const result = await getRecordDetailApi<CommonRes<Record>>({
    recordId: props.recordId,
  });
  if (result.data.code === 0) {
    record.value = result.data.data;
  }
}
</script>

<style lang="scss" scoped>
.scale {
  position: relative;
  height: 100%;
  img {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
