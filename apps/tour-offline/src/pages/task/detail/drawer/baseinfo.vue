<template>
  <div class="baseinfo">
    <div class="info-item">
      <span class="title">提交人:</span>
      <span class="content-text">{{ record?.submitUserName }}</span>
    </div>
    <div class="info-item">
      <span class="title">提交时间:</span>
      <span class="content-text">{{ record?.submitTime }}</span>
    </div>
    <div class="info-item">
      <span class="title">被记录人:</span>
      <span class="content-text">{{ record?.recoreUserName }}</span>
    </div>
    <div class="info-item">
      <span class="title">被记录机构:</span>
      <span class="content-text">{{ record?.recordOrgName }}</span>
    </div>
    <div class="info-item">
      <span class="title">评语:</span>
      <span class="content-text">{{ record?.lectureComment }}</span>
    </div>
    <div class="info-item">
      <span class="title">上传:</span>
      <span class="content-text uploads">
        <div
          v-for="(attr, index) in record?.attrList"
          @click="ysShowPreview(attr.filePath)"
          :key="index"
          class="upload-box-item"
        >
          <ysIcon v-if="attr.type === 1" class="videoIcon" type="iconguankan" />
          <ysIcon v-else class="videoIcon" type="iconbofang" />

          <img :src="isCdn(attr.smallPath)" alt="" />
        </div>
      </span>
    </div>
    <div class="info-item">
      <span class="title">签到位置:</span>
      <span class="content-text">{{ record?.submitAddress }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { getRecordDetailApi } from "@/api";
import { Record } from "@/entity";
import { CommonRes, common } from "@ys/tools";
import { ysIcon, ysShowPreview } from "@ys/ui";
const { isCdn } = common;
const props = defineProps<{
  recordId: string;
}>();

const record = ref<Record>();
onMounted(() => {
  getTaskRecord();
});
async function getTaskRecord() {
  const result = await getRecordDetailApi<CommonRes<Record>>({
    recordId: props.recordId,
  });
  if (result.data.code === 0) {
    record.value = result.data.data;
  }
}
</script>

<style lang="scss" scoped>
.baseinfo {
  margin-top: 20px;
  .info-item {
    margin-bottom: 28px;
    display: flex;
    .title {
      flex: 0 0 81px;
      width: 81px;
      text-align: right;
    }
    .content-text {
      margin-left: 8px;
      word-break: break-all;
      color: #262626;
    }
  }
}

.uploads {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  .upload-box-item {
    cursor: pointer;
    position: relative;
    aspect-ratio: 1;
    background: #f5f5f5;
    border-radius: 4px;
    &:hover .videoIcon {
      display: block;
    }
    .videoIcon {
      display: none;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 30px;
    }
    img {
      border-radius: 4px;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
