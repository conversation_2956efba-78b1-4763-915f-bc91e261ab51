<template>
  <div class="layout">
    <Header />
    <ys-admin-layout :menus="newMenList"></ys-admin-layout>
  </div>
</template>

<script setup lang="ts">
import Header from "@/components/header.vue";
import { ysAdminLayout } from "@ys/ui";
import { CommonRes } from "@ys/tools";
import { Menu } from "@ys/ui/components/ysAdminLayout/subMenu.vue";
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { getPermissionApi } from "@/api";
const router = useRouter();
const newMenList = ref<Array<Menu>>([]);

onMounted(async () => {
  const result = await getPermissionApi<CommonRes<string[]>>();
  if (result.data.code === 0) {
    const permission = result.data.data;
    const data = [];
    if (permission.includes("1")) {
      data.push({
        icon: "iconkaoqin2",
        label: "听课任务",
        key: "/task",
      });
    }

    const settingMenu: any = {
      icon: "iconshezhi3",
      label: "系统设置",
      key: "",
      children: [],
    };
    if (permission.includes("2")) {
      data.push(settingMenu);
    }
    if (permission.includes("2-1")) {
      settingMenu.children.push({
        label: "快捷评语",
        key: "/comments",
      });
    }
    if (permission.includes("2-2")) {
      settingMenu.children.push({
        label: "系统设置",
        key: "/system",
      });
    }

    newMenList.value = data;
    const targetMenu = data[0];
    if (targetMenu.key) {
      router.replace(targetMenu.key);
    } else {
      router.replace(targetMenu.children[0].key);
    }
  }
});
</script>

<style lang="scss" scoped>
.layout {
  width: 100%;
  height: 100vh;
  .container {
    width: 100%;
    height: calc(100% - 56px);
  }
}
</style>
