import { request } from "@ys/tools";
const { instance, sd_url } = request;
/* 表单相关 */
export function formGetAllDataApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/formGetAllData`,
    params,
  });
}
export function forminfoSaveDataApi<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/patrol/forminfoSaveData`,
    data,
    method: "post",
  });
}
export const getAnswerByIdApi = <T>(params: any) => {
  return instance<T>({
    url: "/sd-api/patrol/getAnswerById",
    params,
  });
};

/* 听课任务 */
export function createTaskApi<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/base/edit.do`,
    data,
    method: "post",
  });
}
export function getTaskListApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/base/page.do`,
    params,
  });
}
export function getTaskByIdApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/base/get.do`,
    params,
  });
}
export function delTaskApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/base/delete.do`,
    params,
  });
}
export function changeTaskStatusApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/base/status.do`,
    params,
  });
}
// =========
export function listExpertUserByName<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/schedule/users.do`,
    params,
  });
}

export function addTaskScheduleApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/schedule/add.do`,
    params,
  });
}
export function getTaskScheduleApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/schedule/page.do`,
    params,
  });
}
export function delTaskScheduleApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/schedule/remove.do`,
    params,
  });
}
export function addTaskLocationApi<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/localtion/add.do`,
    data,
    method: "post",
  });
}
export function getTaskLocationApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/localtion/get.do`,
    params,
  });
}
export function getTaskLocationListApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/localtion/list.do`,
    params,
  });
}
/* 快捷评语 */
export function getCommentPageApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/comment/page.do`,
    params,
  });
}
export function editCommentApi<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/comment/edit.do`,
    data,
    method: "post",
  });
}
export function sortCommentApi<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/comment/all/sort.do`,
    data,
    method: "post",
  });
}
export function sortSimpleCommentApi<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/comment/simple/sort.do`,
    data,
    method: "post",
  });
}
export function delCommentPageApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/comment/delete.do`,
    params,
  });
}
/* 系统设置 */
export function getSystemApi<T>() {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/system/get.do`,
  });
}
export function updateSystemApi<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/system/edit.do`,
    data,
    method: "post",
  });
}

/* 任务记录详情 */
export function getTaskRecordApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/record/page.do`,
    params,
  });
}
export function getRecordDetailApi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/task/record/get.do`,
    params,
  });
}
export function getPermissionApi<T>() {
  return instance<T>({
    url: `${sd_url}/patrol/lecture/menu/permission`,
  });
}
