<template>
  <section class="header">
    <div class="logo">
      <img v-if="system.systemPic" :src="isCdn(system.systemPic)" />
      <span>{{ system.systemName }}</span>
    </div>
    <div id="commonHead"></div>
  </section>
</template>

<script setup lang="ts">
import { common, CommonRes } from "@ys/tools";
import { onMounted } from "vue";
import { getSystem, system } from "@/pages/setting/system/system";
const { isCdn } = common;
onMounted(() => {
  (window as any).yskjConfig.createHeadOperation("commonHead", {
    isPureBackground: false,
  });
  getSystem();
});
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 60px;
  padding: 0 24px;
  box-sizing: border-box;
  background: #3e8ef7;
  .logo {
    display: flex;
    align-items: center;
    img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
    span {
      margin-left: 8px;
      color: #fff;
      font-size: 20px;
      font-weight: bold;
    }
  }
  #commonHead {
    height: 60px;
  }
}
</style>
