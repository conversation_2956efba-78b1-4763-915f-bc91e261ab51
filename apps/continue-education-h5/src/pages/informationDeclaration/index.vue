<template>
  <div class="declareSearch">
    <div class="declare_box">
      <van-form @submit="onSubmit" ref="formRef">
        <van-cell-group inset>
          <van-field
            v-model="form.resourceName"
            maxlength="50"
            input-align="right"
            clearable
            required
            name="申报项目"
            label="申报项目"
            placeholder="请输入"
          />
          <van-field
            v-model="form.username"
            input-align="right"
            readonly
            clearable
            required
            name="姓名"
            label="姓名"
            placeholder="请输入"
          />
          <van-field
            v-model="form.idCard"
            input-align="right"
            clearable
            required
            name="身份证号"
            label="身份证号"
            placeholder="请输入"
          />
          <van-field
            v-model="form.orgname"
            input-align="right"
            readonly
            required
            name="所属机构"
            label="所属机构"
            placeholder="请输入"
          />
          <van-field
            v-model="dateValue"
            required
            input-align="right"
            is-link
            name="选择年度"
            label="选择年度"
            placeholder="选择年度"
            @click="showDatePicker = true"
          />
          <van-field
            v-model="trainLevelName"
            required
            input-align="right"
            is-link
            name="培训级别"
            label="培训级别"
            placeholder="请选择"
            @click="showTranLevel = true"
          />
          <van-field
            v-model="joinRuleName"
            required
            input-align="right"
            is-link
            name="角色参与"
            label="角色参与"
            placeholder="请选择"
            @click="showjoinRole = true"
          />
          <van-field
            v-model="stageName"
            input-align="right"
            required
            is-link
            name="学段"
            label="学段"
            placeholder="请选择"
            @click="showStage = true"
          />
          <van-field
            v-model="subjectName"
            input-align="right"
            required
            is-link
            name="任教学科"
            label="任教学科"
            placeholder="请选择"
            @click="showSubject = true"
          />
          <van-field
            v-model="form.phone"
            input-align="right"
            required
            type="digit"
            clearable
            name="手机号"
            label="手机号"
            placeholder="请输入"
          />
          <van-field
            v-model="form.studyTime"
            label-width="100px"
            required
            input-align="right"
            is-link
            name="学习开始时间"
            label="学习开始时间"
            placeholder="请选择"
            @click="showStudyStartTime = true"
          />
          <van-field
            v-model="form.studyEndTime"
            required
            input-align="right"
            label-width="100px"
            label-align="left"
            is-link
            name="学习结束时间"
            label="学习结束时间"
            placeholder="请选择"
            @click="showStudyEndTime = true"
          />
          <van-field
            v-model="form.studyAddress"
            input-align="right"
            required
            clearable
            is-link
            name="学习地点"
            label="学习地点"
            placeholder="请输入"
          />
          <van-field
            v-model="resourceTypeName"
            required
            input-align="right"
            is-link
            name="申报类型"
            label="申报类型"
            placeholder="请选择"
            @click="showChechType = true"
          />
          <van-field
            v-model="form.classHour"
            input-align="right"
            type="digit"
            clearable
            required
            name="认证学时"
            label="认证学时"
            extra="学时"
          >
            <template #extra>
              <span>学时</span>
            </template>
          </van-field>
          <div class="attachment">
            <div class="head">
              <span>
                <span style="color: red; margin-right: 4px">*</span>附件</span
              >
              <ysIcon
                type="iconyuanxingxinzeng"
                @click="uoladAttachMent"
                style="font-size: 25px; color: #808080"
              />
            </div>
            <div class="attachmentList" v-if="form.attachments.length > 0">
              <div
                class="attItem"
                @click="showPreview(item)"
                v-for="(item, index) in form.attachments"
                :key="index"
              >
                <span>
                  <ysIcon
                    :type="imgType(item.attachmentName)"
                    style="font-size: 22px"
                  />
                </span>
                <span style="margin-left: 13px" class="name text-overflow">
                  {{ item.attachmentName }}</span
                >
                <span class="delBox">
                  <ysIcon
                    type="iconshan_chu"
                    style="font-size: 22px"
                    @click.stop="handleDeleteFile(index)"
                  />
                </span>
              </div>
            </div>
          </div>
        </van-cell-group>
        <div class="fix_btn">
          <van-button
            round
            block
            type="default"
            native-type="submit"
            @click="router.back()"
          >
            取消
          </van-button>
          <van-button
            round
            block
            type="primary"
            @click="handleSave"
            native-type="submit"
            style="margin-left: 12px"
          >
            提交
          </van-button>
        </div>
      </van-form>
    </div>
    <!-- 年度选择 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-picker
        title="选择年度"
        :columns="dateList"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
    <!-- 培训级别 -->
    <van-popup v-model:show="showTranLevel" position="bottom">
      <van-picker
        title="选择培训级别"
        :columns="tringLevelList"
        @confirm="onLevelConfirm"
        @cancel="showTranLevel = false"
      />
    </van-popup>
    <!-- 角色参与 -->
    <van-popup v-model:show="showjoinRole" position="bottom">
      <van-picker
        title="选择角色"
        :columns="partList"
        @confirm="onjoinConfirm"
        @cancel="showjoinRole = false"
      />
    </van-popup>
    <!-- 学段 -->
    <van-popup v-model:show="showStage" position="bottom">
      <van-picker
        title="选择学段"
        :columns="stageList"
        @confirm="onstageConfirm"
        @cancel="showStage = false"
      />
    </van-popup>
    <!-- 任教学科 -->
    <van-popup v-model:show="showSubject" position="bottom">
      <van-picker
        title="选择学科"
        :columns="subjectList"
        @confirm="onsubjectConfirm"
        @cancel="showSubject = false"
      />
    </van-popup>
    <!-- 学习开始时间 -->
    <van-popup v-model:show="showStudyStartTime" position="bottom">
      <van-date-picker
        type="time"
        v-model="currentDate"
        title="选择日期"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onStudyStartConfirm"
        @cancel="showStudyStartTime = false"
      />
    </van-popup>
    <!-- 学习结束时间 -->
    <van-popup v-model:show="showStudyEndTime" position="bottom">
      <van-date-picker
        type="time"
        v-model="currentDate"
        title="选择日期"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onStudyEndConfirm"
        @cancel="showStudyEndTime = false"
      />
    </van-popup>
    <!-- 申报类型 -->
    <van-popup v-model:show="showChechType" position="bottom">
      <van-picker
        :columns="plainOptions"
        @confirm="onCheckTypeConfirm"
        @cancel="showChechType = false"
      />
    </van-popup>
    <van-overlay :show="loading">
      <div class="wrapper" @click.stop>
        <div class="block">
          <img src="@/asssts/image/raceloading.gif" alt="" />
          <span>上传中...</span>
        </div>
      </div>
    </van-overlay>
    <!-- <preview :show="showPreviewOverlay" :file="objFile" @close="showPreviewOverlay = false" /> -->
  </div>
</template>

<script lang="ts" setup>
import preview from "@/components/preview.vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import {
  saveResource,
  dictGetByType,
  getResourceDetail,
  getSubjectByGrade,
  getSystemSet,
  getUserInfo,
} from "@/api";
import { ysIcon, ysShowPreviewH5 } from "@ys/ui";
import { common } from "@ys/tools";
import { showToast, FormInstance } from "vant";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
/* --------------- data --------------- */
//#region
const router = useRouter();
const { upload, isCdn } = common;
export interface formProps {
  orgname: string;
  username: string;
  eduResourceId: string;
  userId: string;
  orgid: string;
  idCard: string;
  startTime: string;
  endTime: string;
  stageIds: string | null;
  subjectIds: string | null;
  phone: string;
  studyTime: any;
  studyEndTime: string;
  studyAddress: string;
  resourceName: string;
  resourceType: string;
  classHour: string;
  attachments: attachmentItemProp[];
  trainLevel: "";
  joinRole: "";
}
const tringLevelList = ref([
  { value: 1, text: "国家级" },
  { value: 2, text: "省级" },
  { value: 3, text: "市级" },
  { value: 4, text: "县级" },
  { value: 5, text: "校级" },
]);
const partList = ref([
  { value: 1, text: "主讲人" },
  { value: 2, text: "学员" },
]);
interface stageItemProp {
  code: string;
  id: string;
  text: string;
  type: string;
}

interface subjectItemProp {
  id: string;
  name: string;
}

interface attachmentItemProp {
  attachmentName: string;
  attachmentPath: string;
}
const showChechType = ref(false);
const showjoinRole = ref(false);
const showDatePicker = ref(false);
const showTranLevel = ref(false);
const showStage = ref(false);
const showSubject = ref(false);
// const showStudyTime = ref(false)
const showStudyStartTime = ref(false);
const showStudyEndTime = ref(false);
const dateList = ref<any>([]);
const dateValue = ref("");
const time = ref<any>("");
const username = ref("");
const orgname = ref("");
const idCard = ref("");
const dateValueId = ref("");
const trainLevelName = ref("");
const joinRuleName = ref("");
const stageName = ref("");
const subjectName = ref("");
const StudyTime = ref("");
const resourceTypeName = ref("");
const currentDate = ref<any>([]);
const minDate = new Date(1900, 0, 1);
const maxDate = new Date(2099, 12, 31);
const form: formProps = reactive({
  orgname: "",
  username: "",
  eduResourceId: "",
  userId: "",
  orgid: "",
  idCard: "",
  studyTime: "",
  studyEndTime: "",
  startTime: "",
  endTime: "",
  stageIds: null,
  subjectIds: null,
  phone: "",
  studyAddress: "",
  resourceName: "",
  resourceType: "1",
  classHour: "",
  attachments: [],
  trainLevel: "",
  joinRole: "",
});
const showPreviewOverlay = ref(false);
const plainOptions = ref<any>([
  { text: "灵活性", value: "1" },
  { text: "规范性", value: "2" },
]);

const stageList = ref<Array<stageItemProp>>([]);
const subjectList = ref<Array<subjectItemProp>>([]);
const loading = ref(false);
const formRef = ref<any>(null);
const objFile = ref<any>(null);
//#endregion

/* --------------- methods --------------- */
//#region
const showPreview = (val: any) => {
  ysShowPreviewH5(isCdn(val.attachmentPath), val.attachmentName);
};
// 获取详情
const handleGetResourceDetail = async () => {
  try {
    const response = await getResourceDetail({ id: form.eduResourceId });
    const res = response.data as any;
    if (res.code == 0 && res.success) {
      const response2 = (await getUserInfo()) as any;
      const userInfoData = response2.data.data as any;
      const data = res.data;
      dateValueId.value = data.eduSystemSetId;
      for (let i of dateList.value) {
        if (i.eduSystemSetId == dateValueId.value) {
          dateValue.value = `${i.name}年度`;
        }
      }
      username.value = data.userName;
      orgname.value = data.orgName;
      form.userId = data.userId;
      form.orgid = data.orgid;
      form.idCard = data.idCard;
      form.startTime = dayjs(new Date(data.startTime)).format("YYYY-MM");
      form.endTime = dayjs(new Date(data.endTime)).format("YYYY-MM");
      form.stageIds = data.stageIds;
      form.subjectIds = data.subjectIds;
      if (form.stageIds) {
        handleGetSubject(form.stageIds);
        stageName.value = stageList.value.filter(
          (item) => (item.code as any) == form.stageIds
        )[0].text as any;
      }
      if (form.subjectIds) {
        setTimeout(() => {
          for (let i of subjectList.value) {
            if (i.id == form.subjectIds) {
              subjectName.value = i.name;
            }
          }
        }, 100);
      }
      form.phone = data.phone || "";
      form.studyTime = data.studyTime.split(" ")[0];
      form.studyEndTime = data.studyEndTime.split(" ")[0];
      time.value = data.studyTime;
      form.resourceName = data.resourceName;
      form.resourceType = String(data.resourceType);
      resourceTypeName.value = plainOptions.value.filter(
        (item: any) => item.value == form.resourceType
      )[0].text;
      form.classHour = String(data.classHour);
      form.trainLevel = data.trainLevel;
      trainLevelName.value = tringLevelList.value.filter(
        (item) => (item.value as any) == form.trainLevel
      )[0].text as any;
      form.joinRole = data.joinRole;
      joinRuleName.value = partList.value.filter(
        (item) => (item.value as any) == form.joinRole
      )[0].text as any;
      form.studyAddress = data.studyAddress;
      if (data.attachments) {
        data.attachments.forEach((item: attachmentItemProp) => {
          let obj: attachmentItemProp = {
            attachmentName: "",
            attachmentPath: "",
          };
          obj.attachmentName = item.attachmentName;
          obj.attachmentPath = item.attachmentPath;
          form.attachments.push(obj);
        });
      }
    }
  } catch (error) {}
};
// 提交申报
const handleSave = async () => {
  if (!form.resourceName) {
    showToast("请输入项目名称");
    return false;
  }
  if (!form.username) {
    showToast("请输入姓名");
    return false;
  }
  if (!form.idCard) {
    showToast("请输入身份证号");
    return false;
  }
  if (!form.orgid) {
    showToast("请输入所属机构");
    return false;
  }
  if (!form.startTime) {
    showToast("请选择年度");
    return false;
  }
  if (!form.trainLevel) {
    showToast("请选择培训级别");
    return false;
  }
  if (!form.joinRole) {
    showToast("请选择参与角色");
    return false;
  }
  if (!form.stageIds) {
    showToast("学段不能为空");
    return false;
  }
  if (!form.subjectIds) {
    showToast("任教学科不能为空");
    return false;
  }
  if (!form.phone) {
    showToast("手机号不能为空");
    return false;
  }
  if (!form.studyTime) {
    showToast("请选择学习开始时间");
    return false;
  }
  if (!form.studyEndTime) {
    showToast("请选择学习结束时间");
    return false;
  }
  if (!form.studyAddress) {
    showToast("请选择学习地点");
    return false;
  }
  if (!form.resourceType) {
    showToast("请选择申报类型");
    return false;
  }
  if (!form.classHour) {
    showToast("请输入认证学时");
    return;
  }

  if (form.attachments.length == 0) {
    showToast("请上传附件");
    return;
  }
  const params = JSON.parse(JSON.stringify(form));
  params.eduSystemSetId = dateValueId.value;
  delete params.orgname;
  delete params.username;
  const response = await saveResource(params);
  loading.value = false;
  const res = response.data as any;
  if (res.code == 0 && res.success) {
    showToast("申报完成,即将返回");
    setTimeout(() => {
      router.back();
    }, 1000);
  } else {
    showToast(res.msg);
  }
};
const handleDeleteFile = (index: number) => {
  form.attachments.splice(index, 1);
};

const imgType = (val: any) => {
  let icon = "";
  if (val.toLowerCase().includes("doc") || val.toLowerCase().includes("docx")) {
    icon = "iconword";
  } else if (
    val.toLowerCase().includes("ppt") ||
    val.toLowerCase().includes("pptx")
  ) {
    icon = "iconppt";
  } else if (val.toLowerCase().includes("pdf")) {
    icon = "iconpdf";
  } else {
    icon = "icontupian4";
  }
  return icon;
};
function uoladAttachMent() {
  const input = document.createElement("input");
  input.type = "file";
  input.onchange = handleFile;
  document.body.appendChild(input);
  input.click();
  document.body.removeChild(input);
}
async function handleFile(e: any) {
  const suffixList = ["doc", "docx", "ppt", "pptx", "pdf", "png", "jpg"];
  let file = e.target.files[0];
  const suffix = file.name.slice(file.name.lastIndexOf(".") + 1);
  if (!suffixList.includes(suffix)) {
    showToast("文件格式不支持");
    return;
  }
  let size = file.size / 1048576;
  if (size > 50) {
    showToast("最大50M,最多上传10个文件");
    return;
  }
  if (form.attachments && form.attachments.length >= 10) {
    showToast("最大50M,最多上传10个文件");
    return;
  }
  loading.value = true;
  let res = await upload(e);
  if (res) {
    const item: attachmentItemProp = {
      attachmentName: file.name || res.filename,
      attachmentPath: res.file.pdfUrl || res.filepath,
    };
    form.attachments.push(item);
    loading.value = false;
  }
}
const handleGetSubject = async (stageId: any) => {
  const params = {
    subjectType: "378953758199259136",
    periodId: stageId,
  };
  const response = await getSubjectByGrade(params);
  const res = response.data as any;
  const data = res.data || [];
  subjectList.value = data.map((item: any) => {
    return { ...item, text: item.name, value: item.id };
  });
};
const onStudyStartConfirm = (val: any) => {
  form.studyTime = val.selectedValues.join("-");
  showStudyStartTime.value = false;
};
const onStudyEndConfirm = (val: any) => {
  form.studyEndTime = val.selectedValues.join("-");
  showStudyEndTime.value = false;
};
const onCheckTypeConfirm = (val: any) => {
  resourceTypeName.value = val.selectedOptions[0].text;
  form.resourceType = val.selectedOptions[0].value;
  showChechType.value = false;
};
const onsubjectConfirm = (val: any) => {
  form.subjectIds = val.selectedOptions[0].id;
  subjectName.value = val.selectedOptions[0].text;
  showSubject.value = false;
};
const onstageConfirm = (val: any) => {
  form.stageIds = val.selectedOptions[0].code;
  stageName.value = val.selectedOptions[0].text;
  subjectName.value = "";
  form.subjectIds = "";
  showStage.value = false;
  handleGetSubject(form.stageIds);
};
const onjoinConfirm = (val: any) => {
  joinRuleName.value = val.selectedOptions[0].text;
  form.joinRole = val.selectedOptions[0].value;
  showjoinRole.value = false;
};
const onDateConfirm = (val: any) => {
  form.startTime = val.selectedOptions[0].startTime;
  form.endTime = val.selectedOptions[0].endTime;
  dateValueId.value = val.selectedOptions[0].eduSystemSetId;
  dateValue.value = `${val.selectedOptions[0].name}年度`;
  showDatePicker.value = false;
};
const onLevelConfirm = (val: any) => {
  trainLevelName.value = val.selectedOptions[0].text;
  form.trainLevel = val.selectedOptions[0].value;
  showTranLevel.value = false;
};
const handleGetStage = async () => {
  const params = { type: "stage" };
  const response = await dictGetByType(params);
  const res = response.data as any;
  const data = res.data || [];
  stageList.value = data.map((item: any) => {
    return { ...item, value: item.id };
  });
};
const onSubmit = () => {};
const getTime = async () => {
  let responce = (await getSystemSet()) as any;
  const res = responce.data.data;
  dateList.value = res.map((item: any) => {
    if (item.name == null) {
      item.name = `${item.startTime.split("-")[0]}-${item.endTime.split("-")[0]}`;
    }
    item.text = item.name;
    item.value = item.eduSystemSetId;
    return item;
  });

  if (!router.currentRoute.value.query.id) {
    dateValue.value = `${dateList.value[0].name}年度`;
    dateValueId.value = dateList.value[0].eduSystemSetId;
    form.startTime = dateList.value[0].startTime;
    form.endTime = dateList.value[0].endTime;
  }

  // dateValue.value = [(res.startTime), (res.endTime)];
  // dateHackValue.value = [(res.startTime), (res.endTime)];
  // date.value = [dayjs(res.startTime), dayjs(res.endTime)];
};
const getUserInfoData = async () => {
  const response = (await getUserInfo()) as any;
  const res = response.data;
  const data = res.data;
  username.value = data.name;
  form.username = data.name;
  form.orgname = data.orgName;
  orgname.value = data.orgName;
  idCard.value = data.IDCard || "";
  form.idCard = data.IDCard || "";
  form.userId = data.id;
  form.orgid = data.orgId;
};
const getSysTime = () => {
  let year = new Date().getFullYear();
  let month = new Date().getMonth() + 1;
  let day = new Date().getDate();
  let arr = [
    `${year}`,
    `${month > 9 ? month : `0${month}`}`,
    `${day > 9 ? day : `0${day}`}`,
  ];
  currentDate.value = arr;
};
onMounted(() => {
  getUserInfoData();
  getTime();
  handleGetStage();
  getSysTime();
  if (router.currentRoute.value.query.id) {
    // 编辑
    form.eduResourceId = router.currentRoute.value.query.id as any;
    handleGetResourceDetail();
  }
});
//#endregion
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .block {
    width: 120px;
    height: 120px;
    // background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    color: #ffffff;

    img {
      width: 20px;
      height: 20px;
    }
  }
}

.declareSearch {
  min-height: 100vh;
  background: #f2f2f6;
  padding: 8px 0;

  .declare_box {
    margin-bottom: 100px;

    .van-cell-group--inset {
      margin: 0;
      border-radius: 0;

      .attachment {
        padding: 0 15px 16px 15px !important;
        min-height: 60px;
        font-size: 15px;
        color: #4d4d4d;

        // display: flex;
        // align-items: center;
        // justify-content: space-between;
        .head {
          height: 60px;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .attachmentList {
          .attItem {
            height: 48px;
            line-height: 48px;
            background: #f2f2f6;
            border-radius: 4px;
            padding: 0 8px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;

            .name {
              width: 80%;
            }

            .delBox {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              flex: 1;
            }
          }
        }
      }

      .van-cell {
        line-height: 40px;
        align-items: center;
      }
    }

    // .van-field__label{
    //   display: flex;
    //   align-items: center
    // }
  }

  .fix_btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 68px;
    background: #ffffff;
    box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    padding: 0 16px;
  }
}
</style>
