<template>
  <div class="auto_container plan_container mt24 bcf br4">
    <Header
      :allOrMe="allOrMe"
      :allMe="allMe"
      @changeModel="(num: number) => (model = num)"
      @allMe="(str: number) => (allMe = str)"
      @onlyToday="(bol: boolean) => (today = bol)"
      @weekChange="weekChange"
      @monthChange="monthChange"
    />
    <component
      class="scrollbars"
      style="height: calc(100% - 72px)"
      :is="
        model == 0
          ? section.ScheduleList
          : model == 1
            ? section.WeekCalendar
            : section.MonthCalendar
      "
      :timeLine="timeLineArr"
      :listSlide="listSlide"
      :week="week"
      :weekData="weekData"
      :month="month"
      :monthData="monthData"
      @currentEnd="debounceListToend"
    />
    <load v-if="flag" />
  </div>
  <div
    class="createActiveButton"
    v-if="liveResearchOpen || interactiveResearchOpen"
    @click="() => (showCreateAvtiveBox = true)"
  >
    <div class="add">
      <ysIcon type="icontianjia" class="icon" />
      <!-- <plus-outlined style="color: #fff" /> -->
    </div>
    <div class="create">新建</div>
  </div>
  <div class="activeModal" v-if="showCreateAvtiveBox">
    <createActive
      :liveResearchOpen="liveResearchOpen"
      :interactiveResearchOpen="interactiveResearchOpen"
      @choseModal="showCreateAvtiveBox = false"
    />
  </div>
</template>

<script lang="ts" setup>
import createActive from "./common/createActive.vue";
import { PlusOutlined } from "@ant-design/icons-vue";
import { ysIcon } from "@ys/ui";
import { ref, onMounted, shallowRef, watch } from "vue";
import {
  getReleaseActiveConfig,
  getTimeLine1,
  getWeekCalendar,
  getMonthCalendar,
} from "@/request/plan";
import Header from "./common/header.vue";
import ScheduleList from "./common/scheduleList.vue";
import MonthCalendar from "./common/monthCalendar.vue";
import WeekCalendar from "./common/weekCalendar.vue";
import load from "@/components/loading.vue";
import { selectType } from "@/assets/types";
import { pagationType } from "@/assets/types";
import dayjs from "dayjs";
import { Empty } from "ant-design-vue";
onMounted(() => {
  getTimeLine();
  getCreateActivePermission();
});
defineProps<{ powerArr: any }>();
/* --------------- data --------------- */
//#region
const flag = ref(false);
const section = shallowRef({
  ScheduleList,
  MonthCalendar,
  WeekCalendar,
});
const model = ref<number>(0);
const timeLineArr = ref<Array<any>>([]);
const allMe = ref<number>(2);
const allOrMe = ref<Array<selectType>>([
  { label: "全部", value: 2 },
  { label: "我的", value: 1 },
]);
/* 只看当天 */
const today = ref<boolean>(false);
/* 列表滑动 */
const listSlide = ref<boolean>(true);
const pages = ref<pagationType>({
  pageNo: 1,
  pageSize: 12,
  total: 0,
});
/* 周 */
const week = ref<any>(dayjs().format("YYYY-MM-DD"));
const weekData = ref<Array<any>>([[], [], [], [], [], [], []]);
/* 月 */
const month = ref<any>(dayjs(new Date()).format("YYYY-MM"));
const monthData = ref<Array<any>>([]);
const showCreateAvtiveBox = ref(false);
const liveResearchOpen = ref(true); /* 直播教研   */
const interactiveResearchOpen = ref(true); /* 互动校验 */

//#endregion

/* --------------- methods --------------- */
//#region
/* 全部我的 */
watch(
  () => allMe.value,
  () => {
    if (model.value == 0) {
      resetList();
    } else if (model.value == 1) getWeekData();
    else getMonthData();
  }
);
/* 只看今天 */
watch(
  () => today.value,
  () => {
    resetList();
  }
);
/* 模块切换 */
watch(
  () => model.value,
  (num: number) => {
    num == 0 ? resetList() : num == 1 ? getWeekData() : getMonthData();
  }
);

// 获取是否有管理中心权限
function getCreateActivePermission() {
  getReleaseActiveConfig().then((res: any) => {
    liveResearchOpen.value = res.data.data.liveResearchOpen == 1 ? true : false;
    interactiveResearchOpen.value =
      res.data.data.interactiveResearchOpen == 1 ? true : false;
  });
}
/* 列表 */
function getTimeLine() {
  flag.value = true;
  let params: any = {
    currPage: pages.value.pageNo,
    pageSize: 12,
    own: allMe.value,
    onlyToday: today.value ? 1 : 2,
  };
  getTimeLine1(params).then((res: any) => {
    flag.value = false;
    if (res.data.code == 0) {
      if (res.data.data.length > 0) {
        timeLineArr.value = [...timeLineArr.value, ...res.data.data];
      }
    }
  });
}

const timer = ref<any>();
function debounceListToend(s: any) {
  clearTimeout(timer.value);
  timer.value = null;
  timer.value = setTimeout(() => {
    handleListToend();
    clearTimeout(timer.value);
    timer.value = null;
  }, 400);
}
function handleListToend() {
  pages.value.pageNo += 1;
  getTimeLine();
}
function resetList() {
  pages.value.pageNo = 1;
  timeLineArr.value = [];
  getTimeLine();
}
/* 周 */
function getWeekData() {
  flag.value = true;
  getWeekCalendar({ date: week.value, own: allMe.value }).then((res: any) => {
    flag.value = false;
    if (res && res.data && res.data.code == 0) {
      weekData.value = res.data.data;
    } else weekData.value = [];
  });
}
function weekChange(newWeek: string) {
  week.value = newWeek;
  getWeekData();
}
/* 月 */
function getMonthData() {
  flag.value = true;
  getMonthCalendar({ date: month.value + "-01", own: allMe.value }).then(
    (res: any) => {
      flag.value = false;
      if (res.data.code == 0) {
        monthData.value = res.data.data;
      }
    }
  );
}
function monthChange(newMonth: string) {
  month.value = newMonth;
  getMonthData();
}

//#endregion
</script>

<style lang="scss" scoped>
.activeModal {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 3000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.plan_container {
  margin-bottom: 24px;
  padding: 24px 24px;
  height: calc(100vh - 108px);
}

.createActiveButton {
  width: 56px;
  height: 56px;
  background: linear-gradient(180deg, #268eff 0%, #007aff 100%);
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.16);
  border-radius: 50px 50px 50px 50px;
  opacity: 1;
  position: absolute;
  bottom: 25%;
  right: 3%;
  padding-top: 8px;
  // line-height: 28px;
  text-align: center;
  color: #fff;
  cursor: pointer;
  .add {
    .icon {
      font-size: 18px;
    }
  }

  .create {
    text-align: center;
    color: #ffffff;
    // opacity: .6;
  }
}
</style>
