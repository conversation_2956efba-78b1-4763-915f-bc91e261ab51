<template>
  <section class="header flex_js mb20">
    <div class="left flex_ac">
      <span class="title fb">教研日程</span>
      <div
        v-for="item in props.allOrMe"
        :key="item.value"
        :class="[
          'all_me',
          'mr24',
          'cursor',
          props.allMe == item.value ? 'active' : '',
        ]"
        @click="emit('allMe', item.value)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="right flex_ac">
      <ysIcon
        v-for="(item, i) in icons"
        :key="i"
        :class="['model_icon', 'cursor', item]"
        :style="{ color: i == model ? '#007AFF' : '#8C8C8C' }"
        :type="item"
        @click="model = i"
      />
      <span class="flex_ac" style="margin: 0 18px 0 0; color: #e5e5e5">
        |
      </span>
      <div v-if="model == 0">
        <a-checkbox v-model:checked="today">只看今天</a-checkbox>
      </div>
      <WeekSelect
        v-if="model == 1"
        @timeChange="(week: string) => emit('weekChange', week)"
      />
      <MonthSelect
        v-if="model == 2"
        width="84px"
        @timeChange="(month: string) => emit('monthChange', month)"
      />
      <!-- 查看更多 -->
      <div v-if="props.allMe == 1" class="flex_ac c8c">
        <span style="margin: 0 14px; color: #e5e5e5"> | </span>
        <span class="cursor" @click="handleToActivity"> 更多 &gt; </span>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import { selectType } from "@/assets/types";
import { ysIcon } from "@ys/ui";
import WeekSelect from "@/components/weekSelect.vue";
import MonthSelect from "@/components/monthSelect.vue";
import { common } from "@ys/tools";
const { bureauBlankOpen } = common;

interface Props {
  allMe: number;
  allOrMe: Array<selectType>;
}
const props = defineProps<Props>();

const emit = defineEmits([
  "changeModel",
  "allMe",
  "onlyToday",
  "weekChange",
  "monthChange",
]);
/* ------ data ------ */
//#region

const icons = reactive<Array<string>>(["iconliebiao2", "iconzhou", "iconyue"]);
const model = ref<number>(0);
const today = ref<boolean>(false);
//#endregion
/* 切换查看类型 */
watch(
  () => model.value,
  (num) => {
    emit("changeModel", num);
  }
);
/* 只看当天 */
watch(
  () => today.value,
  (bol: boolean) => {
    emit("onlyToday", bol);
  }
);
/* ------ methods ------ */
//#region

function handleToActivity(): void {
  bureauBlankOpen(`/yskt/a/#/mine/${localStorage.getItem("userId")}/active`);
}

//#endregion
</script>
<style lang="scss" scoped>
.title {
  margin-right: 32px;
  font-size: 16px;
  color: #262626;
}

.all_me {
  padding: 3px 12px;
}

.active {
  color: #007aff;
  background-color: #e8f3ff;
  border-radius: 50px;
}

.model_icon {
  margin-right: 18px;
  font-size: 18px;
}
</style>
