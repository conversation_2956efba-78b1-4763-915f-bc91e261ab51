<template>
  <div class="video-audio-brain">
    <div class="head">
      <div class="head-title">
        <img src="@/assets/images/robot.svg" width="22" alt="" />
        <span>影像大脑</span>
      </div>
      <div class="head-right">
        <div class="switch">
          <span>在字幕中显示对应角色:</span>
          <a-switch
            v-model:checked="captionOpenSpeaker"
            :unCheckedValue="2"
            :checkedValue="1"
            @change="onUserRoleChange"
          />
        </div>
        <a-button class="add" type="primary" @click="onAdd">添加</a-button>
      </div>
    </div>
    <ul class="brain-list" ref="target">
      <li v-for="item in taskGroupList" :key="item.id">
        <div
          class="handler"
          ref="pointerContainer"
          v-if="item.taskGroupStatus != 0 && item.expertStatus != 0"
        >
          <!-- v-if="item.taskGroupStatus != 0 || item.expertStatus != 0" -->
          <!-- <span class="preview" @click="onPreview(item)">预览</span> -->
          <a-dropdown :placement="'bottomRight'">
            <div class="more">
              <ysIcon class="icon" type="icongengduo11" />
            </div>
            <template #overlay>
              <a-menu>
                <a-menu-item v-if="item.taskGroupStatus == 1">
                  <a href="javascript:;" @click="onPreview(item)">预览</a>
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:;" @click="onEditName(item)">编辑名称</a>
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:;" @click="onReTask(item)">重新分析</a>
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:;" @click="onNewExpert(item)"
                    >师训专家分析</a
                  >
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:;" @click="onDelTask(item)">删除</a>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        <div class="left">
          <div v-if="!item.edit" class="title" @click="onPreview(item)">
            {{ item.groupName }}
          </div>
          <div v-else :id="item.id">
            <a-input
              style="width: 320px; margin-bottom: 16px"
              v-model:value="item.input"
              :maxlength="30"
            ></a-input>
            <a-button
              style="margin-left: 8px"
              @click="onSaveName(item)"
              type="primary"
              >保存</a-button
            >
          </div>
          <p class="name text-overflow">
            教师画面：{{ item.teacherViewName || "--" }}
          </p>
          <p class="name text-overflow">
            学生画面：{{ item.studentViewName || "--" }}
          </p>
          <div class="info">
            <div v-if="item.taskType == 1">
              类型：一般课程
            </div>
            <div v-else-if="item.taskType == 2">类型：微格教室</div>
            <div v-else-if="item.taskType == 3">类型：同步课堂</div>
            <div
              class="status"
              v-if="item.taskGroupStatus == 0 || item.expertStatus == 0"
            >
              <span>状态:</span>
              <i class="dot ing"></i>
              <span>分析中</span>
            </div>
            <div class="status" v-else-if="item.taskGroupStatus == 1">
              <span>状态:</span>
              <i class="dot success"></i>
              <span>分析成功</span>
            </div>
            <div class="status" v-else-if="item.taskGroupStatus == 2">
              <span>状态:</span>
              <i class="dot error"></i>
              <span>分析失败</span>
            </div>
            <!-- expertStatus -1 完全没有申请过， 1 成功 0 正在分析 2 分析失败 -->
            <div class="lecturerName">
              主讲人：{{ item.lecturerName || "--" }}
            </div>
            <div
              class="expert"
              v-if="item.expertStatus && item.expertStatus == 1"
            >
              专家分析
            </div>
          </div>
        </div>
        <div class="right">
          <img v-if="item.teacherViewPic" :src="isCdn(item.teacherViewPic)" />
          <img v-if="item.studentViewPic" :src="isCdn(item.studentViewPic)" />
        </div>
      </li>
    </ul>
  </div>

  <addDrawer
    :visible="childrenDrawer"
    @close="onCloseAddDrawer"
    @ok="onOkAddDrawer"
  />
</template>

<script lang="ts" setup>
import { onClickOutside } from "@vueuse/core";
import { message, Modal } from "ant-design-vue";
import { ysIcon } from "@ys/ui";
import { CommonRes, common } from "@ys/tools";
import { useRoute } from "vue-router";
import { onMounted, ref, inject, Ref, createVNode, watch } from "vue";
import addDrawer from "./addDrawer.vue";
import {
  newExpertAiTasks,
  reAiTaskGroup,
  removeTaskGroup,
  selectEventAiTaskGroups,
  updateAiTaskGroup,
  updateOpenSpeaker,
  updateAiTaskGroupSmallClass,
  removeTaskGroupSmallClass,
  reAiTaskGroupSmallClass,
  newExpertAiTasksSmallClass,
} from "./api";
import { getAiAuth } from "@/pages/main/player/playerBody/chatInfo/useChat";
import { aiTaskGroupItemType } from "./entity";

const route = useRoute();
const basicInfo = inject<Ref<BasicInfo>>("basicInfo");
const { bureauBlankOpen, isCdn } = common;

const captionOpenSpeaker = ref(
  basicInfo?.value.eventOtherInfo.captionOpenSpeaker
);
const childrenDrawer = ref(false);

const taskGroupList = ref<aiTaskGroupItemType[]>([]);

const target = ref(null);
onClickOutside(target, (event) => {
  taskGroupList.value.forEach((item: aiTaskGroupItemType) => {
    item.edit = false;
    item.input = item.groupName;
  });
});

const onAdd = () => {
  childrenDrawer.value = true;
};

const onCloseAddDrawer = () => {
  childrenDrawer.value = false;
};
const onOkAddDrawer = () => {
  onCloseAddDrawer();
  getAiTaskGroupsData();
};

const getAiTaskGroupsData = async () => {
  try {
    const response = await selectEventAiTaskGroups({
      eventId: route.params.id,
    });
    const res = response.data as any;
    if (res.code == 0) {
      taskGroupList.value = res.data.map((item: aiTaskGroupItemType) => {
        return {
          ...item,
          edit: false,
          input: "",
        };
      });
    }
  } catch (error) {
    console.error("selectEventAiTaskGroups", error);
  }
};

const onReTask = (item: aiTaskGroupItemType) => {
  let content = item.hasExpert
    ? "此操作将删除当前的分析数据，专家分析数据不受影响"
    : "此操作将删除当前的分析数据";
  Modal.confirm({
    title: createVNode(
      "div",
      { style: { "font-weight": "bold" } },
      "确认重新分析吗?"
    ),
    content,
    centered: true,
    async onOk() {
      const parasm = {
        groupId: item.id,
        gradeId: basicInfo?.value.eventOtherInfo.eventOtherInfoGradeId,
        stageId: basicInfo?.value.eventOtherInfo.eventOtherInfoStageId,
        subjectId: basicInfo?.value.eventOtherInfo.eventOtherInfoSubjectId,
        subjectName: basicInfo?.value.eventOtherInfo.subjectName,
      };
      let response = null;
      if (item.taskType == 1 || item.taskType == 3) {
        response = await reAiTaskGroup(parasm);
      } else {
        response = await reAiTaskGroupSmallClass(parasm);
      }
      const res = response.data as any;
      if (res.code === 0) {
        message.success(res.msg);
        getAiTaskGroupsData();
      }
    },
  });
};

const onNewExpert = async (item: aiTaskGroupItemType) => {
  if (item.expertStatus == 0) {
    message.warn("正在分析中...");
    return false;
  }
  if (item.expertStatus == 1 || item.expertStatus == 2) {
    Modal.confirm({
      title: createVNode(
        "div",
        { style: { "font-weight": "bold" } },
        "确认专家重新分析吗?"
      ),
      content: "此操作将删除当前的专家分析数据，其他分析不受影像",
      centered: true,
      async onOk() {
        const parasm = {
          groupId: item.id,
          gradeId: basicInfo?.value.eventOtherInfo.eventOtherInfoGradeId,
          stageId: basicInfo?.value.eventOtherInfo.eventOtherInfoStageId,
          subjectId: basicInfo?.value.eventOtherInfo.eventOtherInfoSubjectId,
          subjectName: basicInfo?.value.eventOtherInfo.subjectName,
        };

        let response = null;
        if (item.taskType == 1 || item.taskType == 3) {
          response = await newExpertAiTasks(parasm);
        } else {
          response = await newExpertAiTasksSmallClass(parasm);
        }

        // const response = await newExpertAiTasks(parasm);
        const res = response.data as any;
        if (res.code === 0) {
          getAiTaskGroupsData();
        }
      },
    });
  } else {
    const parasm = {
      groupId: item.id,
      gradeId: basicInfo?.value.eventOtherInfo.eventOtherInfoGradeId,
      stageId: basicInfo?.value.eventOtherInfo.eventOtherInfoStageId,
      subjectId: basicInfo?.value.eventOtherInfo.eventOtherInfoSubjectId,
      subjectName: basicInfo?.value.eventOtherInfo.subjectName,
    };
    // const response = await newExpertAiTasks(parasm);
    let response = null;
    if (item.taskType == 1 || item.taskType == 3) {
      response = await newExpertAiTasks(parasm);
    } else {
      response = await newExpertAiTasksSmallClass(parasm);
    }
    const res = response.data as any;
    if (res.code === 0) {
      getAiTaskGroupsData();
    }
  }
};

const onDelTask = (item: aiTaskGroupItemType) => {
  Modal.confirm({
    title: createVNode(
      "div",
      { style: { "font-weight": "bold" } },
      "确认删除所有分析数据吗?"
    ),
    centered: true,
    async onOk() {
      let response = null;
      if (item.taskType == 1 || item.taskType == 3) {
        response = await removeTaskGroup({
          groupId: item.id,
        });
      } else {
        response = await removeTaskGroupSmallClass({
          groupId: item.id,
        });
      }
      const res = response.data as any;
      if (res.code === 0) {
        getAiTaskGroupsData();
      }
    },
  });
};

const onPreview = (item: aiTaskGroupItemType) => {
  if (item.taskGroupStatus == 1) {
    // bureauBlankOpen(`#/active-ai/${route.params.id}?groupId=${item.id}`);
    if (item.taskType == 2) {
      bureauBlankOpen(
        `/yskt/ys/ai/#/active-ai/${route.params.id}/1/${getAiAuth()}?groupId=${item.id}&taskType=2`
      );
    }
    if (item.taskType == 1 || item.taskType == 3) {
      bureauBlankOpen(
        `/yskt/ys/ai/#/active-ai/${route.params.id}/1/${getAiAuth()}?groupId=${item.id}&taskType=${item.taskType}`
      );
    }
  }
};

async function onUserRoleChange(checked: number) {
  await updateOpenSpeaker<CommonRes<null>>({
    eventId: route.params.id,
    openSpeaker: checked,
  });
}

const onEditName = (item: aiTaskGroupItemType) => {
  item.edit = true;
  item.input = item.groupName;
  console.log(item);
};

const onSaveName = async (item: aiTaskGroupItemType) => {
  try {
    if (item.input?.trim() == "") {
      message.error("名称不能为空");
      return false;
    }
    const params = {
      groupId: item.id,
      groupName: item.input,
    };
    console.log("params", params);
    let response = null;
    if (item.taskType == 1) {
      response = await updateAiTaskGroup(params);
    } else {
      response = await updateAiTaskGroupSmallClass(params);
    }
    const res = response.data as any;
    if (res.code == 0) {
      getAiTaskGroupsData();
    }
  } catch (error) {}
};

onMounted(() => {
  getAiTaskGroupsData();
});
</script>

<style lang="scss" scoped>
.video-audio-brain {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.head {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .head-title {
    span {
      margin-left: 8px;
      color: #262626;
    }
  }
  .head-right {
    display: flex;
    align-items: center;
    .switch {
      display: flex;
      align-items: center;
      span {
        margin-right: 8px;
      }
    }
    .add {
      margin-left: 24px;
    }
  }
}

.brain-list {
  margin: 0;
  padding: 0;
  width: 100%;
  list-style: none;
  li {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin-top: 20px;
    width: 100%;
    padding: 20px;
    border: 1px solid #e5e5e5;
    box-sizing: border-box;
    border-radius: 4px 4px 4px 4px;
    .handler {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: absolute;
      top: 20px;
      right: 18px;
      /* width: 74px; */
      height: 22px;
      line-height: 22px;
      .preview {
        color: #007aff;
        cursor: pointer;
      }
      .more {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 22px;
        height: 22px;
        &:hover {
          background: #e6f2ff;
          .icon {
            color: #007aff;
          }
          cursor: pointer;
        }
        .icon {
          font-size: 16px;
        }
      }
    }
    .title {
      font-weight: bold;
      margin-bottom: 16px;
      color: #007aff;
      line-height: 22px;
      cursor: pointer;
    }
    .name {
      max-width: 424px;
      color: #262626;
      line-height: 22px;
    }

    .info {
      display: flex;
      align-items: center;
      .status {
        margin-left: 20px;
        display: flex;
        align-items: center;
        .dot {
          margin: 8px;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          &.ing {
            background: #007aff;
            box-shadow: 0px 0px 0px 2px rgba(0, 122, 255, 0.2);
          }
          &.success {
            background: #17be6b;
          }
          &.error {
            background: red;
          }
        }
      }
      .lecturerName {
        margin-left: 20px;
      }
      .expert {
        margin-left: 16px;
        width: 68px;
        height: 24px;
        text-align: center;
        line-height: 22px;
        font-size: 13px;
        background: #fffbe6;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #ffe07a;
        color: #ffaa00;
      }
    }
    .right {
      margin-right: 24px;
      img {
        width: 174px;
        height: 98px;
        &:last-child {
          margin-left: 24px;
        }
      }
    }
  }
}
</style>
