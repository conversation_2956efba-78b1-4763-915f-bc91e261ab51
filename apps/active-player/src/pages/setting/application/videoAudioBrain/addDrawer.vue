<template>
  <a-drawer
    class="addDrawerCustomClass"
    v-model:visible="props.visible"
    width="1000"
    title="添加"
    :closable="false"
    :maskClosable="false"
  >
    <template #extra>
      <ysIcon class="icon" type="icon-close" @click="onClose" />
    </template>
    <section class="addDrawer">
      <!-- <header>
        <div class="header-left">添加</div>
        <div class="header-right">
          <ysIcon class="icon" type="icon-search-1" />
        </div>
      </header> -->
      <section class="content">
        <div class="content-left">
          <div class="course-type">
            <div>课程类型</div>
            <div class="radio-item"></div>
            <a-radio-group
              style="margin-top: 12px"
              @change="onTypeChange"
              v-model:value="type"
            >
              <a-radio :style="radioStyle" :value="1">
                <div class="course-type-title">
                  <span>一般课程</span>
                  <span>既有老师又有学生的一般课程</span>
                </div>
              </a-radio>
              <a-radio :style="radioStyle" :value="2">
                <div class="course-type-title">
                  <span>微格教室</span>
                  <span>只有老师没有学生的模拟练习课程</span>
                </div>
              </a-radio>
              <a-radio :style="radioStyle" :value="3">
                <div class="course-type-title">
                  <span>同步课堂</span>
                  <span>和远端进行远程互动的课程</span>
                </div>
              </a-radio>
            </a-radio-group>
          </div>
          <div class="name">
            <div>分析任务名称:</div>
            <a-input
              style="width: 328px; margin-top: 10px"
              v-model:value="groupName"
              placeholder="请输入"
              :maxlength="20"
            />
          </div>

          <!-- <div class="videoType" v-if="type == 1">
            <h3>视频类型：</h3>
            <a-radio-group
              v-model:value="videoType"
              @change="onVideoTypeChange"
            >
              <a-radio :value="1">资源模式</a-radio>
              <a-radio :value="2">电影模式</a-radio>
            </a-radio-group>
          </div> -->

          <div class="checkList">
            <div
              class="checkItem"
              @dragover="ondragover"
              @drop="ondrop($event, 1)"
            >
              <h3 class="title1" v-if="type == 1 || type == 3">
                教师画面
              </h3>
              <div class="dropBox">
                <div class="data" v-if="teacherItem">
                  <div class="video-item current">
                    <img
                      :src="isCdn(teacherItem.eventAttrSmallImgUrl)"
                      alt=""
                    />
                    <p class="text-overflow">
                      {{ teacherItem.userName }}
                      <span v-if="teacherItem.userName">-</span>
                      {{ teacherItem.eventAttrName }}
                    </p>
                  </div>
                </div>
                <div class="no" v-else>请将右侧待选视频拖入</div>
              </div>
            </div>
            <div
              v-if="type == 1 || type == 3"
              class="checkItem"
              @dragover="ondragover"
              @drop="ondrop($event, 2)"
            >
              <h3 class="title1">学生画面</h3>
              <div class="dropBox">
                <div class="data" v-if="studentItem">
                  <div class="video-item current">
                    <img
                      :src="isCdn(studentItem.eventAttrSmallImgUrl)"
                      alt=""
                    />
                    <p class="text-overflow">
                      {{ studentItem.userName }}
                      <span v-if="studentItem.userName">-</span>
                      {{ studentItem.eventAttrName }}
                    </p>
                  </div>
                </div>
                <div class="no" v-else>请将右侧待选视频拖入</div>
              </div>
            </div>
          </div>

          <div class="speakers" v-if="(type == 1 || type == 3) && eventInfo">
            <div class="title">
              <span>教案：</span>
            </div>
            <a-button
              :style="[docPath ? '' : 'border-color: #007aff;color: #007aff']"
              type="dashed"
              :size="'large'"
              :disabled="docPath ? true : false"
              @click="handleUploadDoc"
            >
              <template #icon>
                <UploadOutlined />
              </template>
              上传
            </a-button>
            <ul class="doc-list" v-if="docName && docPath">
              <li>
                <div class="doc-left">
                  <ysIcon class="icon1" type="iconfujian1" />
                  <span class="text text-overflow">{{ docName }}</span>
                </div>
                <div class="doc-right">
                  <ysIcon
                    class="icon1"
                    type="iconshanchudefuben"
                    @click="onDelDocPath"
                  />
                </div>
              </li>
            </ul>
            <div style="margin-top: 8px; color: #8c8c8c">支持doc、docx</div>
          </div>

          <div class="speakers" v-if="eventInfo">
            <div class="title">
              <span>评分表：</span>
            </div>
            <a-select v-model:value="formId" style="width: 328px" placeholder="请选择">
              <a-select-option
                v-for="item in formList"
                :value="item.id"
                >{{ item.name }}</a-select-option
              >
            </a-select>
          </div>

          <div class="speakers" v-if="eventInfo">
            <div class="title">
              <i>*</i>
              <span>主讲人</span>
            </div>
            <a-select ref="select" v-model:value="lecture" style="width: 328px">
              <a-select-option
                v-for="speaker in eventInfo.speakers"
                :value="speaker.userId"
                >{{ speaker.nickName }}</a-select-option
              >
            </a-select>
          </div>

          <div class="submit">
            <a-button
              type="primary"
              :disabled="disabledSubmit"
              @click="onSubmit"
              >提交</a-button
            >
            <span>请至少放入一个画面</span>
          </div>
        </div>
        <div class="content-right">
          <h3 class="title1">录像视频</h3>

          <div class="video-list">
            <div
              v-for="item in videoList"
              :key="item.eventAttrId"
              class="video-item"
              draggable="true"
              @dragstart="ondragstart($event, item)"
            >
              <img :src="isCdn(item.eventAttrSmallImgUrl)" alt="" />
              <p class="text-overflow">
                {{ item.userName }} <span v-if="item.userName">-</span>
                {{ item.eventAttrName }}
              </p>
            </div>
          </div>
        </div>
      </section>
    </section>
  </a-drawer>
</template>

<script setup lang="ts">
import { UploadOutlined } from "@ant-design/icons-vue";
import { message } from "ant-design-vue";
import { ysIcon } from "@ys/ui";
import { CommonRes, common } from "@ys/tools";
import {
  selectEventVideosNoAi,
  newAiTaskGroup,
  newAiTaskGroupSmallClass,
  selectEnableEvaluationForms
} from "./api";
import { useRoute } from "vue-router";
import { ref, computed, Ref, inject, watch, reactive } from "vue";
import dayjs from "dayjs";
import { getFileType, upload } from "@ys/tools/common/file";
const { isCdn } = common;

type videoItemType = {
  eventAttrId: string;
  eventAttrName: string;
  eventAttrOwn: string;
  eventAttrPath: string;
  eventAttrSmallImgUrl: string;
  eventAttrType: number;
  userName: string;
};

const radioStyle = reactive({
  display: "flex",
  height: "30px",
  lineHeight: "30px",
});

const props = defineProps<{
  visible: boolean;
}>();
const emit = defineEmits(["close", "ok"]);
const basicInfo = inject<Ref<BasicInfo>>("basicInfo");
const eventInfo = inject<Ref<EventInfo>>("eventInfo");

const route = useRoute();
const type = ref(1);
const groupName = ref("");
// const videoType = ref(1);
const lecture = ref<any>(null);
const videoList = ref<videoItemType[]>([]);
const teacherItem = ref<videoItemType | null>(null);
const studentItem = ref<videoItemType | null>(null);

const disabledSubmit = computed(() => {
  if (
    (teacherItem.value?.eventAttrId || studentItem.value?.eventAttrId) &&
    groupName.value
  ) {
    return false;
  }
  return true;
});

const onclear = () => {
  groupName.value = "";
  videoList.value = [];
  teacherItem.value = null;
  studentItem.value = null;
  docName.value = "";
  docPath.value = "";
  formId.value = null;
};

const onClose = () => {
  onclear();
  emit("close");
};

const getVideoListData = async () => {
  const response = await selectEventVideosNoAi<CommonRes<any>>({
    eventId: route.params.id,
  });
  const res = response.data;
  if (res.code == 0) {
    videoList.value = res.data;
  }
};

const ondragstart = (event: any, item: videoItemType) => {
  event.dataTransfer.setData("setItem", JSON.stringify(item));
};

const ondragover = (event: any) => {
  event.preventDefault(); // 阻止默认行为
  event.dataTransfer.dropEffect = "move";
};

const ondrop = (event: any, type: number) => {
  const item = JSON.parse(event.dataTransfer.getData("setItem"));
  if (type == 1) {
    teacherItem.value = item;
  } else {
    studentItem.value = item;
  }
};
const onTypeChange = () => {
  teacherItem.value = null;
  studentItem.value = null;
  if (type.value == 2) {
    // videoType.value = 1;
    docName.value = "";
    docPath.value = "";
  }
};

// 上传教案
const docName = ref("");
const docPath = ref("");
const handleUploadDoc = () => {
  const input = document.createElement("input");
  input.type = "file";
  input.onchange = onUploadDoc;
  document.body.appendChild(input);
  input.click();
  document.body.removeChild(input);
};

const onUploadDoc = async (e: any) => {
  const file = e.target.files[0];
  const type = getFileType(file.name);
  console.log("type", type);
  if (type === "doc" || type === "docx") {
    const res = await upload(e);
    console.log("res", res);
    docName.value = file.name;
    docPath.value = res.filepath;
  } else {
    message.warn("请上传doc或docx文件");
  }
};

const onDelDocPath = () => {
  docName.value = "";
  docPath.value = "";
};

// const onVideoTypeChange = () => {
//   studentItem.value = null;
// };

const formId = ref<string | null>(null); // 评分表id
const formList = ref<any[]>([]);
const getForm = async () => {
  const response = await selectEnableEvaluationForms<CommonRes<any>>({});
  const res = response.data;
  if (res.code == 0) {
    formList.value = res.data;
  }
}

const onSubmit = async () => {
  try {
    if (teacherItem.value?.eventAttrId == studentItem.value?.eventAttrId) {
      message.error("请选择不同的录像");
      return;
    }
    const params: any = {
      sourceId: route.params.id,
      sourceType: 1, // 源类型1活动2资源
      sourceTime: basicInfo?.value.eventInfo.eventStartTime,
      lecture: lecture.value,
      groupName: groupName.value,
      subjectName: basicInfo?.value.eventOtherInfo.subjectName,
      stageId: basicInfo?.value.eventOtherInfo.eventOtherInfoStageId,
      gradeId: basicInfo?.value.eventOtherInfo.eventOtherInfoGradeId,
      subjectId: basicInfo?.value.eventOtherInfo.eventOtherInfoSubjectId,
      taskType: type.value, // 当视频类型选择为电影模式时，参数为 3
      docName: docName.value,
      docPath: docPath.value,
      formId: formId.value || "",
    };

    if (studentItem.value?.eventAttrId) {
      params.studentObjId = studentItem.value?.eventAttrId;
      params.studentObjType = 1;
    }
    if (teacherItem.value?.eventAttrId) {
      params.teacherObjId = teacherItem.value?.eventAttrId;
      params.teacherObjType = 1;
    }
    let response = null;
    if (type.value == 1 || type.value == 3) {
      response = await newAiTaskGroup(params);
    } else {
      response = await newAiTaskGroupSmallClass(params);
    }
    const res = response.data as any;
    if (res.code == 0) {
      message.success(res.msg);
      onclear();
      emit("ok");
    }
  } catch (error) {}
};

watch(
  () => props.visible,
  (newV) => {
    if (newV) {
      groupName.value = "分析任务" + dayjs(new Date()).format("YYYYMMDDHHmm");
      getVideoListData();
      getForm();
    }
  }
);

watch(
  () => eventInfo?.value,
  (newV) => {
    if (newV) {
      if (eventInfo?.value.speakers && eventInfo?.value.speakers.length > 0) {
        lecture.value = eventInfo?.value.speakers[0].userId;
      }
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>

<style lang="scss" scoped>
.icon {
  cursor: pointer;
  font-size: 20px;
}
.addDrawer {
  width: 100%;
  height: calc(100vh - 56px);
  header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 56px;
    padding: 0 24px;
    box-sizing: border-box;
    box-shadow: inset 0px -1px 0px 0px #f0f0f0;
    .header-left {
      color: #262626;
    }
    .header-right {
    }
  }
  .title1 {
    font-size: 14px;
    color: #262626;
  }
  .video-item {
    margin-bottom: 20px;
    position: relative;
    width: 278px;
    height: 162px;
    border-radius: 4px;
    &.current {
      width: 240px;
      height: 135px;
    }

    img {
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }
    p {
      margin: 0;
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      padding: 0 8px;
      box-sizing: border-box;
      color: #fff;
      background: rgba(0, 0, 0, 0.45);
      border-radius: 0 0 4px 4px;
    }
  }
  .video-list {
    list-style: none;
    height: calc(100vh - 124px);
    overflow-y: auto;
  }
  .content {
    display: flex;
    width: 100%;
    height: calc(100vh - 56px);
    .content-left {
      flex: 1;
      padding: 24px;
      box-sizing: border-box;
      .course-type {
        margin-bottom: 24px;
        .course-type-title {
          span {
            &:first-child {
              color: #262626;
            }
            &:last-child {
              margin-left: 12px;
              color: #8c8c8c;
            }
          }
        }
      }
      .name {
        margin-bottom: 20px;
        /* display: flex;
        align-items: center; */
      }
      // .videoType {
      //   margin-bottom: 16px;
      //   h3 {
      //     font-size: 14px;
      //     color: #262626;
      //     line-height: 22px;
      //   }
      // }

      .checkList {
        display: flex;
        .checkItem {
          margin-right: 20px;
          .dropBox {
            position: relative;
            width: 240px;
            height: 135px;
            background: #f0f0f0;
            .no {
              position: absolute;
              left: 0;
              top: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 240px;
              height: 135px;
              color: #8c8c8c;
              background: #f0f0f0;
            }
          }
        }
      }
      .speakers {
        margin-top: 24px;
        .title {
          margin-bottom: 12px;
          display: flex;
          i {
            font-style: normal;
            color: #f53f3f;
          }
          span {
            margin-left: 4px;
            color: #262626;
          }
        }
      }
      .doc-list {
        margin: 0;
        padding: 0;
        list-style: none;
        li {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 8px;
          width: 328px;
          height: 22px;
          padding: 0 6px;
          box-sizing: border-box;
          background: #f5f5f5;
          border-radius: 0px 0px 0px 0px;
          .icon1 {
            color: #8c8c8c;
          }
          .doc-left {
            display: flex;
            align-items: center;
            .text {
              display: inline-block;
              max-width: 280px;
              margin-left: 3px;
              color: #007aff;
            }
          }
          .doc-right {
            display: flex;
            align-items: center;
            .icon1 {
              cursor: pointer;
            }
          }
        }
      }
      .submit {
        margin-top: 24px;
        span {
          margin-left: 12px;
          color: #ffaa00;
        }
      }
    }

    .content-right {
      flex: 0 0 336px;
      border-left: 1px solid #f0f0f0;
      width: 336px;
      padding: 24px;
      box-sizing: border-box;
    }
  }
}
</style>

<style lang="scss">
.addDrawerCustomClass {
  .ant-drawer-body {
    padding: 0;
  }
}
</style>
