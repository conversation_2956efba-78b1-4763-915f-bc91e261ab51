import { request } from "@ys/tools";
const { instance, sd_url } = request;

export function getInfoForContent<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/settingContent/getInfoForContent`,
    params,
  });
}

export function selectEventVideosNoAi<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/selectEventVideosNoAi`,
    params,
  });
}

export function queryTask<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/event/ai/queryTask`,
    params,
  });
}

export function queryAiTaskGroup<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/queryAiTaskGroup`,
    params,
  });
}

export function newTask<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/event/ai/newTask`,
    params,
  });
}
export function selectEventAttrs<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/event/ai/selectEventAttrs`,
    params,
  });
}
export function selectVisitEventInfo<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/indexNew/selectVisitEventInfo`,
    params,
  });
}

export function getStInfo<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/event/ai/getStData`,
    params,
  });
}

export function updateOpenSpeaker<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/indexPage/updateOpenSpeaker`,
    params,
  });
}
export function getStudentActionData<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/event/ai/getStudentActionData`,
    params,
  });
}

export function getCompareTaskDetail<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/getCompareTaskDetail`,
    params,
  });
}

// export function getEventAttrCompareList<T>(params: any) {
//   return instance<T>({
//     url: `${sd_url}/event/event/ai/getEventAttrCompareList`,
//     params,
//   });
// }

export function selectTaskGroupCompareTasks<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/selectTaskGroupCompareTasks`,
    params,
  });
}

export function getHotPointData<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/getHotPointData`,
    params,
    headers: { hideErrorTip: true },
  });
}

export function exportAiTeachPlan<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/exportAiTeachPlan`,
    params,
    responseType: "blob",
  });
}

export function newTaskByRole<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/event/ai/newTaskByRole`,
    params,
  });
}

export function newAiTaskGroup<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/newAiTaskGroup`,
    method: "post",
    data,
  });
}
export function newAiTaskGroupSmallClass<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/smallClass/newAiTaskGroup`,
    method: "post",
    data,
  });
}

export function updateAiTaskGroup<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/updateAiTaskGroup`,
    method: "post",
    data,
  });
}
export function updateAiTaskGroupSmallClass<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/smallClass/updateAiTaskGroup`,
    method: "post",
    data,
  });
}

export function selectEventAiTaskGroups<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/selectEventAiTaskGroups`,
    params,
  });
}

export function removeTaskGroup<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/removeTaskGroup`,
    params,
  });
}
export function removeTaskGroupSmallClass<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/smallClass/removeTaskGroup`,
    params,
  });
}

export function reAiTaskGroup<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/reAiTaskGroup`,
    method: "post",
    data,
  });
}
export function reAiTaskGroupSmallClass<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/smallClass/reAiTaskGroup`,
    method: "post",
    data,
  });
}

export function newExpertAiTasks<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/newExpertAiTasks`,
    method: "post",
    data,
  });
}
export function newExpertAiTasksSmallClass<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/smallClass/newExpertAiTasks`,
    method: "post",
    data,
  });
}

export function selectEnableEvaluationForms<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/event/ai/selectEnableEvaluationForms`,
    params,
  });
}