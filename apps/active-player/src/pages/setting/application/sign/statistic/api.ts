import { request } from "@ys/tools";
const { instance, sd_url } = request;

export function getCount<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/verification/verification/tv/sign/total.do`,
    params,
  });
}
export function getOrg<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/verification/verification/tv/total/orgs.do`,
    params,
  });
}
export function getArea<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/verification/verification/tv/total/area.do`,
    params,
  });
}
export function getCtiy<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/verification/verification/tv/total/ctiy.do`,
    params,
  });
}
