import { inject, onMounted, ref, watch, Ref } from "vue";
import { getCount, getOrg, getArea, getCtiy } from "./api";
import { Static, StaticCount } from "./entity";
import { CommonRes, common, request } from "@ys/tools";
const { sd_url } = request;
const { down } = common;
const countInfoList = [
  {
    label: "累计签到人数",
    value: 0,
    color: "rgb(23, 190, 107)",
  },
  {
    label: "指定签到",
    value: 0,
    color: "rgb(0, 122, 255)",
  },
  {
    label: "未签到",
    value: 0,
    color: "rgb(245, 110, 83)",
  },
  {
    label: "签到率",
    value: 0,
    color: "rgb(255, 170, 0)",
  },
];
export function useStatistic() {
  const id = inject<Ref<string>>("id");
  const list = ref<Static[]>([]);
  const countList = ref(countInfoList);
  const activeType = ref("1");
  const listLoading = ref(false);
  const exportLoading = ref(false);
  async function getCounts() {
    const result = await getCount<CommonRes<StaticCount>>({
      id: id?.value,
      type: 1,
    });
    const { signAll, probability, unSign, zdSign } = result.data.data;
    countList.value[0].value = signAll;
    countList.value[1].value = zdSign;
    countList.value[2].value = unSign;
    countList.value[3].value = probability;
  }
  async function getRankByOrg() {
    listLoading.value = true;
    const result = await getOrg<CommonRes<Static[]>>({
      id: id?.value,
      type: 1,
    });
    list.value = result.data.data;
    listLoading.value = false;
  }
  async function getRankByArea() {
    listLoading.value = true;
    const result = await getArea<CommonRes<Static[]>>({
      id: id?.value,
      type: 1,
    });
    list.value = result.data.data;
    listLoading.value = false;
  }
  async function getRankByCtiy() {
    listLoading.value = true;
    const result = await getCtiy<CommonRes<Static[]>>({
      id: id?.value,
      type: 1,
    });
    list.value = result.data.data;
    listLoading.value = false;
  }
  function exportExcel() {
    let url = `${sd_url}/verification/verification/tv/total/export.do?id=${id?.value}&type=${activeType.value}`;
    down(url, "签到详情.xls");
  }
  watch(
    () => activeType.value,
    (val) => {
      if (val === "1") {
        getRankByOrg();
      }
      if (val === "2") {
        getRankByArea();
      }
      if (val === "3") {
        getRankByCtiy();
      }
    }
  );
  onMounted(() => {
    getCounts();
    getRankByOrg();
  });
  return {
    listLoading,
    exportLoading,
    activeType,
    list,
    countList,
    exportExcel,
  };
}
