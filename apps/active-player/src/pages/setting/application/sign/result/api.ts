import { request } from "@ys/tools";
const { instance, sd_url } = request;

export function getbases<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/verification/verification/tv/sign/getbases.do`,
    params,
  });
}
export function getsettlement<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/verification/verification/tv/sign/getsettlement.do`,
    params,
  });
}
export function settlement<T>(data: any) {
  return instance<T>({
    url: `${sd_url}/verification/verification/tv/sign/settlement.do`,
    data,
    method: "post",
  });
}

export function detail<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/verification/verification/tv/answer/detail.do`,
    params,
  });
}
export function clear<T>(params: any) {
  return instance<T>({
    url: `${sd_url}/verification/verification/tv/sign/clear.do`,
    params,
  });
}
