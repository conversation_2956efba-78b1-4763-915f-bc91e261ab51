<script lang="ts" setup>
import { inject, onMounted, ref, Ref } from "vue"
import { ysIcon, ysShowConfirmInputModal } from "@ys/ui"
import { CommonRes, common, request } from "@ys/tools";
import { detail, clear } from "./api"
const { down } = common
const emit = defineEmits(['onClear'])
const { sd_url } = request
const id = inject<Ref<string>>("id");
const res = ref(0)
const loading = ref(false);
interface User {
  user_name: string
  organization_name: string
  user_code: string
  res: string
  num: string
}
const list = ref<User[]>([]);

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '姓名',
    dataIndex: 'user_name',
    key: 'user_name',
    align: 'center'
  },
  {
    title: '机构',
    dataIndex: 'organization_name',
    key: 'organization_name',
    ellipsis: true,
    align: 'center'
  },
  {
    title: '手机号',
    dataIndex: 'user_code',
    key: 'user_code',
    ellipsis: true,
    align: 'center'
  },
  {
    title: '指定名单',
    dataIndex: 'res',
    key: 'res',
    ellipsis: true,
    align: 'center'
  },
  {
    title: '签到详情',
    dataIndex: 'num',
    key: 'num',
    width: 120,
    align: 'center'
  },
]
async function getList() {
  loading.value = true
  const result = await detail<CommonRes<User[]>>({
    id: id?.value,
    res: res.value,
    type: 1
  })
  loading.value = false
  list.value = result.data.data
}
onMounted(getList)
function onReCall() {
  ysShowConfirmInputModal('确定要重新结算吗？').then(async _ => {
    const result = await clear<CommonRes<null>>({ id: id?.value, type: 1 })
    if (result.data.code === 0) {
      emit('onClear')
    }
  })
}

</script>

<template>
  <div class="head">
    <div>结果列表</div>
    <a-space>
      <a-select @change="getList" v-model:value="res" style="width: 120px">
        <a-select-option :value="0">全部</a-select-option>
        <a-select-option :value="1">是</a-select-option>
        <a-select-option :value="2">否</a-select-option>
      </a-select>
      <a-button @click="onReCall">重新结算</a-button>
      <a-button @click="down(`${sd_url}/verification/verification/tv/total/exportres.do?id=${id}&type=0`, '签到详情.xls')"
        type="primary">
        <ysIcon type="icondaochu2" />
        导出
      </a-button>
    </a-space>
  </div>
  <a-table :columns="columns" :dataSource="list" :loading="loading" :pagination="false">
    <template #bodyCell="{ column, text, record, index }">
      <template v-if="column.dataIndex === 'index'">
        {{ index + 1 }}
      </template>
      <template v-if="column.dataIndex === 'res'">
        {{ text.length > 3 ? "是" : "否" }}
      </template>
    </template>
  </a-table>
</template>

<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
</style>