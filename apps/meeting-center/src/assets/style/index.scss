.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
}

.loada {
  position: relative;
}

.bred {
  border: 1px solid red;
}

.border_d {
  border: 1px solid #d9d9d9;
}

.bbe5 {
  border-bottom: 1px dashed #e5e5e5;
}

.contentContainer {
  @media screen and (max-width: 1680px) {
    margin: 0 40px;
  }

  @media screen and (min-width: 1680px) {
    width: 1500px;
    margin: 0 auto;
  }
}

.auto_container {
  margin: 0 auto;
}

@media screen and (min-width: 1600px) {
  .auto_container {
    width: 1520px;
  }
}

@media screen and (max-width: 1600px) {
  .auto_container {
    width: 1200px;
  }
}

.p2-overflow {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

.position_r {
  position: relative;
}

.position_a {
  position: absolute;
}

.flex {
  display: flex;
}

.flex1 {
  flex: 1;
}

.flex_center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex_ac {
  display: flex;
  align-items: center;
}

.flex_js {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex_sa {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.basic_title {
  color: #262626;
  font-weight: bold;
  font-family: "黑体";
}
/* lv */

.text_overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: rgba(161, 168, 184, 0.3);
  }
}

.scrollbars {
  overflow: auto;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: rgba(161, 168, 184, 0.3);
  }
}

.cursor {
  cursor: pointer;
}

.br2 {
  border-radius: 2px;
}

.br4 {
  border-radius: 4px;
}

.br50 {
  border-radius: 50%;
}

.c07 {
  color: #007aff !important;
}

.cf {
  color: #fff;
}

.c59 {
  color: #595959;
}

.c8c {
  color: #8c8c8c;
}

.fs16 {
  font-size: 16px;
}

.fs18 {
  font-size: 16px;
}

.b07 {
  background-color: #007aff;
}

.bcf {
  background-color: #fff;
}

.p16 {
  padding: 16px;
}

.p24 {
  padding: 24px;
}

.p1624 {
  padding: 16px 24px;
}

.w100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

.h36 {
  height: 36px;
}

.c_hidden {
  overflow: hidden;
}

.pd10 {
  padding-bottom: 10px;
}

.pd16 {
  padding-bottom: 16px;
}

.mt24 {
  margin-top: 24px;
}

.mb8 {
  margin-bottom: 8px;
}

.mb10 {
  margin-bottom: 10px;
}

.mb16 {
  margin-bottom: 16px;
}

.mb18 {
  margin-bottom: 18px;
}

.mb20 {
  margin-bottom: 20px;
}

.mb22 {
  margin-bottom: 22px;
}

.mb24 {
  margin-bottom: 24px;
}

.mr12 {
  margin-right: 12px;
}

.mr16 {
  margin-right: 16px;
}

.mr24 {
  margin-right: 24px;
}

.fb {
  font-weight: bold;
}

.user_n {
  user-select: none;
}

.spot {
  width: 6px;
  height: 6px;
  border-radius: 3px;
}

/* 备课成果楼层 */
.lesson_card_floor {
  margin-bottom: 24px;
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;
}

.ant-drawer-body {
  padding: 0;
}

/* drawer关闭按钮移到右侧 */
.ant-drawer-close {
  position: absolute;
  right: 20px;
}

/* 统一表格高度 */
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 0 10px !important;
  height: 56px;
}

/* 去掉表格头部小竖线 */
.ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(
    .ant-table-row-expand-icon-cell
  ):not([colspan])::before {
  display: none;
}

/* 排序按钮贴近文字 */
.ant-table-column-sorters {
  justify-content: start;

  .ant-table-column-title {
    flex: none;
  }

  .ant-table-column-sorter {
    margin-left: 10px;
    flex: none;
  }
}

/* modal层级 */
.ant-modal-wrap {
  z-index: 1003;
}

/* 按钮 输入框 选择器高度统一为36px */

.ant-btn {
  border-radius: 4px;
  height: 36px;
}

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
  height: 36px;
}

.ant-select-selection-placeholder,
.ant-select-selection-item {
  line-height: 36px !important;
}
.ys-sign-setting {
  // .ant-select-selection-placeholder,
  // .ant-select-selection-item {
  //   line-height: 22px !important;
  // }
  // .ant-select-selector {
  //     .ant-select-selection-item {
  //         line-height: 34px !important;
  //     }
  // }
  .ant-select-selection-item-content {
    line-height: 22px;
  }
  .ant-select-selection-item-remove {
    line-height: 19px;
  }
}

/* ------ 模块切换 ------ */
//#region

.slide-left-enter-active {
  transition: all 0.1s ease-out;
}

.slide-left-leave-active {
  transition: all 0.4s ease-out;
}

.slide-left-enter-from {
  opacity: 0;
}

.slide-left-leave-to {
  opacity: 0;
}

//#endregion

/* ------ 1 ------ */
//#region
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

//#endregion

/* 气泡确认框按钮高度 */
.popconfirm_btn {
  .ant-btn {
    height: 24px;
  }
}
.ant-popconfirm {
  .ant-btn {
    height: 24px;
    border-radius: 2px;
  }
}

/* 取消card下边线 */
.ant-card-head {
  border: none;
}

.ant-tabs-ink-bar {
  height: 3px !important;
}
