<template>
  <div class="metting_allBox" ref="metting_moreArguments">
    <div class="metting_moreArguments">
      <span style="color: #8c8c8c" class="cursor" @click="back"
        >会议安排&nbsp;/&nbsp;</span
      >
      <span>{{ openCreateBox ? "发起会议" : "我的" }}</span>
      <div class="table_container" v-if="!openCreateBox">
        <div class="head">
          <div class="leftNav">
            <div
              class="item"
              :class="[active == item.id ? 'itemChoose' : '']"
              @click="active = item.id"
              v-for="(item, index) in NavList"
              :key="item.id"
            >
              {{ item.name }}
            </div>
          </div>
          <div class="right">
            <a-button
              type="primary"
              @click="openCreateBox = true"
              v-if="originData.includes('0-6') || originData.includes('0-1')"
              >发起会议</a-button
            >
          </div>
        </div>
        <div class="container">
          <a-table
            :dataSource="listData"
            :columns="columns"
            :pagination="false"
            @change="sortChange"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex == 'meetingName'">
                <div
                  style="color: #007aff"
                  class="text_overflow cursor"
                  @click="goInfo(record)"
                >
                  {{ record.meetingName }}
                </div>
              </template>
              <template v-if="column.dataIndex == 'meetingStartTime'">
                <a-tooltip placement="topLeft">
                  <template #title>
                    <span
                      >{{
                        dayjs(record.meetingStartTime).format(
                          "YYYY-MM-DD HH:mm"
                        )
                      }}~{{
                        dayjs(record.meetingEndTime).format("YYYY-MM-DD HH:mm")
                      }}</span
                    >
                  </template>
                  <div class="text_overflow cursor">
                    {{ getTime(record.meetingStartTime) }}
                    <span
                      v-if="
                        active == '1' &&
                        handleCurrentTime(
                          record.meetingStartTime,
                          record.meetingEndTime
                        )
                      "
                      class="to_have"
                      >进行中</span
                    >
                  </div>
                </a-tooltip>
              </template>
              <template v-if="column.dataIndex == 'labels'">
                <div class="label">
                  <div
                    class="labelItem"
                    v-for="(item1, index) in record.labels.filter(
                      (item2: any, index: any) => index < 3
                    )"
                    :class="[item1.labelName.length > 6 ? 'text-overflow' : '']"
                    :key="index"
                  >
                    <span>{{ item1.labelName }}</span>
                  </div>
                  <a-popover
                    placement="bottomRight"
                    :getPopupContainer="() => $refs.metting_moreArguments"
                    overlayClassName="popoverClass"
                    trigger="click"
                    @visibleChange="
                      (val: boolean) => (listData[index].open = val)
                    "
                  >
                    <template #content>
                      <div class="moreBox scrollbar">
                        <div
                          class="item text-overflow"
                          :title="item3.labelName"
                          v-for="(item3, index) in record.labels.filter(
                            (item2: any, index: any) => index > 2
                          )"
                          :key="index"
                        >
                          {{ item3.labelName }}
                        </div>
                      </div>
                    </template>
                    <UpOutlined
                      v-if="record.labels.length > 3"
                      :class="[record.open ? 'open' : 'close']"
                    />
                    <!-- <div class="cursor labelItem" v-if="record.labels.length > 3">···</div> -->
                  </a-popover>
                </div>
              </template>
              <template v-if="column.dataIndex == 'setting'">
                <div style="display: flex">
                  <span
                    @click="handleToSignDetail(record.meetingBaseId)"
                    class="cursor"
                    style="color: #007aff; margin-right: 8px"
                    >签到记录</span
                  >
                  <a-popconfirm
                    title="是否确认删除?&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
                    ok-text="确定"
                    cancel-text="取消"
                    @confirm="confirm(record.meetingBaseId)"
                  >
                    <span class="cursor" style="color: #007aff">删除</span>
                  </a-popconfirm>
                </div>
              </template>
            </template>
          </a-table>
          <br />
          <ys-pagination
            @change="handlePagingChange"
            :pageNo="pageNo"
            :pageSize="pageSize"
            :total="total"
          />
        </div>
      </div>
      <div v-else class="table_container">
        <createmetting />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ysPagination from "@/components/pagations/index.vue";
import createmetting from "./createMetting.vue";
import { tableColumns } from "@/assets/types";
import { ref, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import { selectMyMeetingTypePage, deleteBaseMeet } from "@/request";
import dayjs from "dayjs";
import { message } from "ant-design-vue";
import { UpOutlined } from "@ant-design/icons-vue";
import permission from "@/hooks/permission";
const { originData } = permission();
const router = useRouter();

/* --------------- data --------------- */
//#region
const openCreateBox = ref(false);
const NavList = ref([
  { id: "1", name: "即将召开" },
  { id: "2", name: "已结束" },
  { id: "0", name: "审核中" },
  { id: "3", name: "未通过" },
]);
const active = ref("1");
const showSuccessBox = ref(false);
const pageNo = ref<number>(1);
const pageSize = ref<number>(10);
const total = ref<number>(0);
const columns = ref<Array<tableColumns>>([
  {
    title: "会议名称",
    dataIndex: "meetingName",
    align: "left",
    width: "35%",
    ellipsis: true,
  },
  {
    title: "开始时间",
    dataIndex: "meetingStartTime",
    align: "left",
    sorter: true,
    width: "30%",
    ellipsis: true,
  },
  {
    title: `标签`,
    dataIndex: "labels",
    align: "left",
    width: "",
    ellipsis: true,
  },
  { title: `操作`, dataIndex: "setting", align: "left", width: "140px" },
]);
const listData = ref<Array<any>>([]);
const sorterValue = ref(1);
//#endregion
watch(
  () => active.value,
  (val) => {
    if (val) {
      pageNo.value = 1;
      getList();
    }
  }
);
/* --------------- methods --------------- */
//#region
function getTime(val: any) {
  let str = "";
  let week = "";
  let weekTime = new Date(val).getDay();
  let time = dayjs(val).format("MM月DD日 HH:mm");
  if (weekTime == 1) {
    week = "周一";
  }
  if (weekTime == 2) {
    week = "周二";
  }
  if (weekTime == 3) {
    week = "周三";
  }
  if (weekTime == 4) {
    week = "周四";
  }
  if (weekTime == 5) {
    week = "周五";
  }
  if (weekTime == 6) {
    week = "周六";
  }
  if (weekTime == 0) {
    week = "周天";
  }
  str = week + " " + time;
  return str;
}
function handleToSignDetail(val: any) {
  router.push(`/signRecord?id=${val}`);
}
function back() {
  router.push("/meetingProtal");
}
function goInfo(val: any) {
  router.push(`/mettingDetails?id=${val.meetingBaseId}&crumbTitle=我的`);
}

function sortChange(page: any, filters: any, sorter: any) {
  console.log(sorter);
  // if (sorter.field == 'startTime') {
  if (sorter.order == "ascend") {
    sorterValue.value = 2;
  } else {
    sorterValue.value = 1;
  }
  // }
  pageNo.value = 1;
  getList();
}
async function confirm(val: any) {
  let params = {
    meetingBaseId: val,
  };
  let res: any = await deleteBaseMeet(params);
  if (res.data.code == 0) {
    message.success("删除成功");
    getList();
  }
}
/* 分页改变 */
function handlePagingChange(no: number, size: number): void {
  pageNo.value = no;
  pageSize.value = size;
  getList();
}
async function getList() {
  let params = {
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    sort: sorterValue.value,
    type: active.value,
  };
  const res = (await selectMyMeetingTypePage(params)) as any;
  listData.value = res.data.data.map((v: any) => {
    return { ...v, open: false };
  });
  total.value = Number(res.data.totalDatas);
}

onMounted(() => {
  getList();
});
//#endregion

function handleCurrentTime(s: number, e: number) {
  let c = Date.now();
  return c > s && c < e;
}
</script>

<style lang="scss" scoped>
::v-deep(.ant-popover-arrow) {
  display: none;
}

.popoverClass {
  .ant-popover-inner-content {
    padding: 0 !important;
  }

  .moreBox {
    max-width: 330px;
    overflow: auto;
    max-height: 144px;
    background: #ffffff;
    border-radius: 0px 0px 4px 4px;
    display: flex;
    flex-wrap: wrap;

    .item {
      padding: 2px;
      margin-right: 8px;
      text-align: center;
      max-width: 94px;
      background: #f7f7f7;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #d9d9d9;
    }
  }
}

.open {
  color: #007aff;
  transition: all 0.4s;
  transform: rotate(0deg);
}

.close {
  color: #8c8c8c;
  transition: all 0.4s;
  transform: rotate(180deg);
}

.metting_allBox {
  padding-bottom: 24px;
  width: 100%;
  min-height: 100vh;
  // border: 1px solid red;
  background: #f0f2f5;

  .metting_moreArguments {
    padding-top: 16px;
    margin: auto;
    width: 83%;
    max-width: 1600px;

    .table_container {
      margin-top: 16px;
      background: #fff;
      // height: 765px;
      border-radius: 4px;
      padding: 15px 24px;

      .head {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .leftNav {
          display: flex;
          align-items: center;

          .item {
            // width: 56px;
            height: 46px;
            margin-right: 30px;
            line-height: 46px;

            cursor: pointer;
          }

          .itemChoose {
            border-bottom: 3px solid #007aff;
          }
        }
      }

      .container {
        margin-top: 16px;

        .label {
          display: flex;
          align-items: center;

          .labelItem {
            margin-right: 10px;
            max-width: 89px;
            padding: 2px 8px;
            background: #f7f7f7;
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
            border: 1px solid #d9d9d9;
          }
        }
      }
    }
  }
}

.to_have {
  padding: 0 2px;
  color: #ffaa00;
  border: 1px solid #ffaa00;
  border-radius: 4px;
  background-color: #ffaa0014;
}
</style>
<style lang="scss">
.metting_allBox {
  .ant-table-column-sorters {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
