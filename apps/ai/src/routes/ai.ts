const aiRouter = [
  {
    path: "/layout",
    component: () => import("@/pages/ai/layout.vue"),
    // redirect: "/ai-course/index",
    children: [
      {
        path: "/ai-course/index",
        component: () => import("@/pages/ai/course/index.vue"),
        meta: {
          title: "AI课程分析",
          key: "ai-course",
          name: "ai-course",
        },
      },
      {
        path: "/ai-class-async/index",
        component: () => import("@/pages/ai/classAsync/index.vue"),
        meta: {
          title: "同课异构分析",
          key: "ai-class-async",
        },
      },
      {
        path: "/data-aggregation/layout",
        component: () => import("@/pages/ai/dataAggregation/layout.vue"),
        redirect: "/data-aggregation/personal-statistics",
        meta: {
          title: "数据汇总分析",
          key: "data-aggregation",
        },
        children: [
          {
            path: "/data-aggregation/personal-statistics",
            component: () =>
              import("@/pages/ai/dataAggregation/personalStatistics/index.vue"),
            meta: {
              title: "个人统计",
              key: "data-aggregation",
              name: "data-aggregation-personal-statistics",
            },
          },
          {
            path: "/data-aggregation/summary-statistics",
            component: () =>
              import("@/pages/ai/dataAggregation/summaryStatistics/index.vue"),
            meta: {
              title: "汇总统计",
              key: "data-aggregation",
              name: "data-aggregation-summary-statistics",
            },
          },
        ],
      },
      {
        path: "/data-aggregation/personal-statistics/:userId",
        component: () =>
          import("@/pages/ai/dataAggregation/personalStatistics/detail.vue"),
        meta: {
          title: "个人统计",
          key: "data-aggregation",
          name: "data-aggregation-personal-statistics-detail",
        },
      },
      {
        path: "/analyse-task/layout",
        component: () => import("@/pages/ai/analyseTask/layout.vue"),
        redirect: "/analyse-task/course-analyse",
        meta: {
          title: "分析任务管理",
          key: "analyse-task",
        },
        children: [
          {
            path: "/analyse-task/course-analyse",
            component: () =>
              import("@/pages/ai/analyseTask/courseAnalyse/index.vue"),
            meta: {
              title: "课程分析",
              key: "analyse-task",
              name: "analyse-task-course-analyse",
            },
          },
          {
            path: "/analyse-task/class-async-analyes",
            component: () =>
              import("@/pages/ai/analyseTask/classAsyncAnalyes/index.vue"),
            meta: {
              title: "同课异构分析",
              key: "analyse-task",
              name: "analyse-task-class-async-analyes",
            },
          },
        ],
      },
      {
        path: "/claStruPortView",
        props: {
          isView: true,
        },
        component: () =>
          import("@/pages/videoAudioBrain/report/_clasStruPort/index.vue"),
        meta: {
          background: "#fff",
        },
      },
      {
        path: "/setting/layout",
        component: () => import("@/pages/ai/setting/layout.vue"),
        redirect: "/setting/evaluation-form",
        meta: {
          title: "设置",
          key: "setting",
        },
        children: [
          {
            path: "/setting/evaluation-form/:tableId?",
            component: () =>
              import("@/pages/ai/setting/evaluationForm/index.vue"),
              meta: {
                title: "评价表设置",
                key: "setting",
                name: "setting-evaluation-form",
              },
          },
        ],
      },
    ],
  },
];

export default aiRouter;
