<template>
  <main class="ai-layout" id="aiLayout">
    <Header v-if="!isInIframe" />
    <div class="container">
      <router-view></router-view>
    </div>
  </main>
</template>

<script setup lang="ts">
import Header from "./header.vue";
import { useRoute } from "vue-router";
import { watch, nextTick } from "vue";

const route = useRoute();

const isInIframe = window !== window.top;

watch(
  () => route,
  (newV) => {
    if (newV.meta.background) {
      nextTick(() => {
        const dom = document.getElementById("aiLayout");
        if (dom) {
          dom.style.background = newV.meta.background as string;
        }
      });
    } else {
      document.getElementById("aiLayout")?.removeAttribute("style");
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.ai-layout {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #f0f2f5;
  .container {
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    width: 100%;
    height: calc(100vh - 60px);
    box-sizing: border-box;
    overflow-y: scroll;
    z-index: 1;
  }
}
</style>
