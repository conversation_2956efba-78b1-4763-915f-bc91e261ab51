<template>
  <div class="course-analyse" v-if="tableId == ''">
    <div class="title">评价表设置</div>
    <div class="content">
      <div class="nav">
        <span>评分表列表</span>
        <a-button type="primary" @click="onAdd">
          <template #icon><PlusOutlined /></template>
          添加
        </a-button>
      </div>

      <a-table
        :columns="columns"
        :row-key="(record: any) => record.index"
        :data-source="dataSource"
        :pagination="false"
        :loading="false"
        @change="onTableChange"
        :scroll="{ x: 1500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'createTime'">
            {{ dayjs(record.createTime).format("YYYY-MM-DD HH:mm") }}
          </template>

          <template v-if="column.dataIndex === 'status'">
            <template v-if="record.status != -1">
              <div class="table-status" v-if="record.status == 1">
                <i class="current"></i>
                <span>启用</span>
              </div>
              <div class="table-status" v-if="record.status == 2">
                <i class="error"></i>
                <span>停用</span>
              </div>
            </template>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-space>
              <span
                class="oprate-span"
                @click="jumpToFormSetting(record)"
                >表单设置</span
              >
              <span
                class="oprate-span"
                @click="onUpdateFormStatus(record)"
                >{{ record.status == 1 ? '停用' : '启用' }}</span
              >
                <a-popover placement="bottom">
                  <template #content>
                    <p style="cursor: pointer;" @click="setEditData(record)">编辑</p>
                    <a-popconfirm
                      title="你确定删除吗？"
                      ok-text="确定"
                      cancel-text="取消"
                      @confirm="onDelete(record.id)"
                    >
                      <div style="cursor: pointer;">删除</div>
                    </a-popconfirm>
                  </template>
                  <span class="oprate-span" style="font-weight: bold;">⋯</span>
                </a-popover>
            </a-space>
          </template>
        </template>
      </a-table>

      <div :style="{ marginTop: '16px', padding: '0', textAlign: 'right' }">
        <a-pagination
          hideOnSinglePage
          show-quick-jumper
          :loading="pageData.loading"
          :current="pageData.currPage"
          :showSizeChanger="true"
          :pageSize="pageData.pageSize"
          :total="pageData.total"
          @change="onPageChange"
          :show-total="(total: number) => `共${total}条`"
        />
      </div>
    </div>

    <!-- 添加弹窗 -->
    <AddModal
      v-model:visible="showAddModal"
      :editData="editData"
      @ok="handleAddOk"
    />
  </div>
  <FormSetting v-else />
</template>

<script lang="ts" setup>
import {
  selectAiForms,
  editForm,
  updateFormStatus,
  deleteForm
} from "@/api/aiSettingController";
import { message } from "ant-design-vue";
import { PlusOutlined } from "@ant-design/icons-vue";
import dayjs from "dayjs";
import { useRoute, useRouter } from "vue-router";
import { ref, reactive, onMounted, watch } from "vue";
import AddModal from "./addModal.vue";
import FormSetting from "./formSetting.vue";

const router = useRouter();
const route = useRoute();
const tableId = ref<string>("");

const columns = [
  {
    dataIndex: "index",
    title: "序号",
    width: 60,
  },
  {
    dataIndex: "name",
    title: "名称",
    width: 288,
    ellipsis: true,
  },
  {
    dataIndex: "formDescribe",
    title: "文字",
    width: 400,
    ellipsis: true,
  },
  {
    dataIndex: "status",
    title: "状态",
    width: 126,
  },
  {
    dataIndex: "userName",
    title: "创建人",
    width: 166,
  },
  {
    dataIndex: "operation",
    title: "操作",
    width: 130,
  }
];

const pageData = reactive({
  currPage: 1,
  pageSize: 10,
  total: 0,
  loading: false,
});

const dataSource = ref([]);

const getAiForms = async () => {
  try {
    pageData.loading = true;
    const response = await selectAiForms({
      currPage: pageData.currPage,
      pageSize: pageData.pageSize,
    });
    pageData.loading = false;

    const res = response.data as any;
    if (res.code === 0) {
      const data = res.data;
      pageData.total = Number(res.totalDatas);
      dataSource.value = data.map((item: any, key: number) => ({
        index: key + 1,
        ...item,
      }));
    }
  } catch (error) {
    console.error("selectAiTaskGroups", error);
  }
};
function onPageChange(pageNo: number, pageSize: number) {
  pageData.currPage = pageNo;
  pageData.pageSize = pageSize;
  getAiForms();
}

const onTableChange: any["onChange"] = (
  pag: { pageSize: number; current: number },
  filters: any,
  sorter: any
) => {
  getAiForms();
};

// 添加
const showAddModal = ref<boolean>(false);
const onAdd = () => {
  showAddModal.value = true;
};
const handleAddOk = (formData: { id?: string; name: string; formDescribe: string }) => {
  const params = {
    id: formData.id || "-1",
    name: formData.name,
    formDescribe: formData.formDescribe,
  }

  editForm(params).then((response) => {
    const res = response.data as any;
    if (res.code === 0) {
      message.success(formData.id ? '编辑成功' : '添加成功');
      getAiForms();
    }
  });
};

// 编辑
interface EditData {
  id: string;
  name: string;
  formDescribe: string;
}
const editData = ref<EditData>({
  id: "",
  name: "",
  formDescribe: ""
});
const setEditData = (record: EditData) => {
  editData.value = {
    id: record.id,
    name: record.name,
    formDescribe: record.formDescribe
  }
  showAddModal.value = true;
}

// 表单设置
const jumpToFormSetting = (record: any) => {
  router.replace({
    path: `/setting/evaluation-form/${record.id}`,
    query: {
      name: record.name
    }
  });
};

// 启用/停用
const onUpdateFormStatus = (record: any) => {
  updateFormStatus({
    id: record.id,
    status: record.status == 1 ? 2 : 1,
  }).then((response) => {
    const res = response.data as any;
    if (res.code === 0) {
      message.success(record.status == 2 ? '启用成功' : '停用成功');
      getAiForms();
    }
  });
};

// 删除
const onDelete = (id: string) => {
  deleteForm({ id }).then((response) => {
    const res = response.data as any;
    if (res.code === 0) {
      message.success('删除成功');
      getAiForms();
    }
  });
};

watch(() => route.params.tableId, (newVal) => {
  tableId.value = newVal as string;
}, {
  immediate: true,
});

onMounted(() => {
  getAiForms();
});
</script>

<style lang="scss" scoped>
.course-analyse {
  width: 100%;
  padding: 24px;
  box-sizing: border-box;
  .content {
    margin-top: 20px;
    width: 100%;
    padding: 20px 24px;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    .nav {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 20px;
    }
    .table-status {
      display: flex;
      align-items: center;
      i {
        width: 6px;
        height: 6px;
        background: #007aff;
        border-radius: 50%;
        &.current {
          background: #17be6b;
        }
        &.error {
          background: red;
        }
      }
      span {
        margin-left: 8px;
      }
    }
    .oprate-span {
      color: #007aff;
      cursor: pointer;
    }
  }
}
</style>
