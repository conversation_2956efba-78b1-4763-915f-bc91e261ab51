<template>
  <a-modal
    :title="editData.id ? '编辑' : '添加'"
    :visible="visible"
    :confirmLoading="loading"
    @cancel="handleCancel"
    :maskClosable="false"
    :width="500"
  >
    <a-form :model="formState" :rules="rules" ref="formRef">
      <a-form-item name="name" label="名称">
        <a-input v-model:value="formState.name" placeholder="请输入" show-count maxlength="50" />
      </a-form-item>
      <a-form-item name="formDescribe" label="描述">
        <a-textarea
          v-model:value="formState.formDescribe"
          placeholder="请输入"
          :rows="4"
          show-count
          maxlength="100"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="handleCancel">取 消</a-button>
      <a-button type="primary" :loading="loading" @click="handleOk">确 定</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, PropType, watch } from 'vue';
import type { FormInstance } from 'ant-design-vue';

interface EditData {
  id?: string;
  name: string;
  formDescribe: string;
}
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object as PropType<EditData>,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'ok', 'cancel']);

const loading = ref(false);
const formRef = ref<FormInstance>();

const formState = ref<EditData>({
  id: "",
  name: "",
  formDescribe: ""
});

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  formDescribe: [{ required: true, message: '请输入描述', trigger: 'blur' }]
};

const resetForm = () => {
  formState.value = {
    id: "",
    name: "",
    formDescribe: ""
  }
};

const handleOk = () => {
  formRef.value?.validate().then(() => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      emit('ok', { ...formState.value });
      emit('update:visible', false);
      resetForm();
    }, 500);
  }).catch(error => {
    console.log('验证失败:', error);
  });
};

const handleCancel = () => {
  emit('update:visible', false);
  resetForm();
};

watch(() => [props.visible, props.editData], (newVal) => {
  if (newVal[0]) {
    if (newVal[1] && (newVal[1] as EditData).id) {
      formState.value = {
        id: (newVal[1] as EditData).id,
        name: (newVal[1] as EditData).name,
        formDescribe: (newVal[1] as EditData).formDescribe
      }
    }
  }
}, { immediate: true });
</script>

<style scoped>
</style>
