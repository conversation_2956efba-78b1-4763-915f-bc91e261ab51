<template>
  <div class="analyse-task-layout">
    <ys-admin-layout style="width: 100%" :menus="newMenList"></ys-admin-layout>
  </div>
</template>

<script setup lang="ts">
import { ysAdminLayout } from "@ys/ui";
import { Menu } from "@ys/ui/components/ysAdminLayout/subMenu.vue";
import { ref } from "vue";

const newMenList = ref<Array<Menu>>([
  {
    icon: "iconbiaoge",
    label: "评价表设置",
    key: "/setting/evaluation-form",
    num: 1,
  },
]);
</script>

<style lang="scss" scoped>
.analyse-task-layout {
  display: flex;
  width: 100%;
  height: 100%;
}
</style>
