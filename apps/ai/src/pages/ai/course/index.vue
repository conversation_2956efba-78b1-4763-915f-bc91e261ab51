<template>
  <ysLoading :loading="exportPdfLoding" style="position: fixed" />
  <div class="ai-course">
    <div class="ai-course-container">
      <div class="nav">
        <div v-if="!from || from !== 'resource'" class="nav-left">
          <div
            class="nav-item"
            :class="[{ active: active == 1 }]"
            @click="onChangeNav(1)"
          >
            课程分析任务
          </div>
          <div
            class="nav-item"
            :class="[{ active: active == 2 }]"
            @click="onChangeNav(2)"
          >
            个人分析报告
          </div>
        </div>
        <p v-else>*按照本人为AI课程分析任务主讲人的所有任务分析得出</p>

        <div class="nav-right" v-if="active == 2">
          <a-select
            ref="select"
            v-model:value="currentYear"
            style="width: 108px"
          >
            <a-select-option :value="item" v-for="item in yearList">{{
              item == "" ? "全部" : item
            }}</a-select-option>
          </a-select>
          <div
            class="nav-right-export"
            @click="handleExportPdf('.analyse-report', '个人分析报告')"
          >
            <ysIcon type="icondaochu" class="icon" />
            <span>导出报告</span>
          </div>
        </div>
      </div>
      <analyTask v-if="active == 1"></analyTask>
      <analyseReport v-if="active == 2"></analyseReport>
    </div>
  </div>
</template>

<script setup lang="ts">
import analyTask from "./analyseTask/index.vue";
import analyseReport from "./analyseReport/index.vue";
import { useExportPdf } from "@/hooks/exportPdf";
import { ysIcon, ysLoading } from "@ys/ui";
import { ref, watch } from "vue";
import { useGetYears } from "./useGetYaers";
import { useRoute } from "vue-router";

const route = useRoute();
const from = route.query.from;

const isInIframe = window !== window.top;

const { handleExportPdf, exportPdfLoding } = useExportPdf();
const active = ref(1);
const onChangeNav = (value: number) => {
  active.value = value;
};

const { currentYear, yearList } = useGetYears();

watch(
  () => route.query.from,
  (val) => {
    if (!val) return;
    if (val == "resource") {
      active.value = 2;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.ai-course {
  width: 100%;
  .ai-course-container {
    // margin: 32px auto 0;
    // width: 1536px;
    margin: v-bind('isInIframe ? "0" : "32px auto 0"');
    width: v-bind('isInIframe ? "100%" : "1536px"');
    .nav {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .nav-left {
        display: flex;
        .nav-item {
          margin-right: 32px;
          width: 116px;
          height: 32px;
          line-height: 32px;
          text-align: center;
          background: #eaeaea;
          border-radius: 900px 900px 900px 900px;
          cursor: pointer;
          &.active {
            color: #007aff;
            background: #cce4ff;
          }
        }
      }
      .nav-right {
        display: flex;
        align-items: center;
        .nav-right-export {
          display: flex;
          align-items: center;
          color: #007aff;
          cursor: pointer;
          .icon {
            font-size: 18px;
          }
          span {
            margin-left: 10px;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1600px) {
  .ai-course {
    .ai-course-container {
      // width: 1200px;
      width: v-bind('isInIframe ? "100%" : "1200px"');
    }
  }
}
</style>
