export type AIStatus = -1 | 0 | 1 | 2;

export interface AiTaskGroupVo {
    createTime: number;
    gradeId: string;
    groupName: string;
    hasExpert: boolean;
    id: string;
    lecturePic: string;
    lecturer: string;
    lecturerName: string;
    stageId: string;
    studentObjId: string;
    studentObjType: number;
    studentViewName: string;
    studentViewPath: string;
    studentViewPic: string;
    subjectId: null;
    taskGroupId: string;
    taskType: number;
    taskGroupStatus: number;
    teacherObjId: string;
    teacherObjType: number;
    teacherViewName: string;
    teacherViewPath: string;
    teacherViewPic: string;
    sourceTime: number;
    tenantid: string;
    reportFileCreated: boolean;
    reportFileUrl: string | null;
}

export interface AiCaptionTask {
    aiTaskGroupId: string;
    createTime: number;
    id: string;
    objId: string;
    objType: number;
    taskId: string;
    taskResult: string;
    taskStatus: number;
}

export interface AiClassActionData {
    dataLength: number;
    aiTaskGroupId: string;
    answer: string;
    blackboardWriting: string;
    discuss: string;
    handRaising: string;
    hotPoint: string;
    id: string;
    listen: string;
    makeInspectionTour: string;
    onStageInteraction: string;
    reading: string;
    recite: string;
    studentOther: string;
    teach: string;
    teacherOther: string;
    writing: string;
}

export interface AiClassActionDataVo {
    dataLength: number;
    listen: any[];
    discuss: any[];
    makeInspectionTour: any[];
    onStageInteraction: any[];
    blackboardWriting: any[];
    reading: any[];
    recite: any[];
    studentOther: any[];
    teach: any[];
    teacherOther: any[];
    writing: any[];
}

export interface MergedSegmentProps {
    blackboards: {
        end: string;
        result: string;
        start: string;
        images: any[];
    }[];
    endTime: string;
    questions: any[];
    startTime: string;
    教学内容: any[];
    教学方式: string;
    教学环节: string;
    时间: string;
}

export interface Attr {
    eventAttrId: string;
    eventAttrName: string;
    eventAttrPath: string;
    taskStatus: number;
    openSpeaker: number;
}

export interface TaskInfo {
    answer: string;
    eventAttrChatTaskId: string;
    taskId: string;
    taskStatus: number;
    questionType: number;
}

export interface Static {
    chData: string;
    rtData: string;
    isOld: boolean;
    // st: string[] | string;
    stData: string;
    word: string;
    hotWord: string;
    // { word: string; frequency: string }[]
    speak: string;
    speakSpeed: string;
    mandarin: string;
    ktcWords: number;
    stStyle: number;
    // | {
    //     student: {
    //       averageSpeed: number;
    //       totalChars: number;
    //       totalMinutes: number;
    //       speedData: number[];
    //       fluency: string;
    //       ktcCounts: any;
    //     };
    //     teacher: {
    //       averageSpeed: number;
    //       totalChars: number;
    //       totalMinutes: number;
    //       speedData: number[];
    //       fluency: string;
    //       ktcCounts: any;
    //     };
    //   }
}

export interface AiClassBehaviorData {
    aiTaskGroupId: string;
    behavior: string;
    engagementScores: string;
    focusScores: string;
    id: string;
}

export const roleStuTabList = [
    {
        label: "课程内容概括",
        questionType: 1,
        icon: "iconwenjian4",
    },
    // {
    //     label: "章节总览",
    //     questionType: 2,
    //     icon: "iconcengji1",
    // },
    {
        label: "思维导图",
        questionType: 3,
        icon: "iconfenzhi",
    },
    {
        label: "考考我",
        questionType: 4,
        icon: "icondengpao",
    },
];

export const roleAssistantTabList = [
    {
        label: "内容总结",
        questionType: 18,
        icon: "iconwenjian2",
    },
    {
        label: "课堂结构",
        questionType: 2,
        icon: "icon-apartment",
    },
    // {
    //   label: "提问分析",
    //   questionType: 22,
    //   icon: "iconkaoshi1",
    // },
    {
        label: "教学设计分析",
        questionType: 23,
        icon: "iconfuwu",
    },
];

export const roleSimTabList = [
    {
        label: "简洁分析",
        questionType: 18,
        icon: "",
    },
];
export const roleTeaTabList = [
    {
        label: "综述",
        questionType: 17,
        icon: "iconwenjian3",
    },
    {
        label: "导入",
        questionType: 5,
        icon: "iconjiaoshi11",
    },
    {
        label: "讲解",
        questionType: 6,
        icon: "iconfayanren",
    },
    {
        label: "提问",
        questionType: 7,
        icon: "iconyuanxingwenhao",
    },
    {
        label: "变化",
        questionType: 8,
        icon: "icontongbu1",
    },
    {
        label: "强化",
        questionType: 9,
        icon: "iconquanxian",
    },
    {
        label: "结束",
        questionType: 10,
        icon: "iconyuanxingluzhi",
    },
];

export function pieGetLength(data: number[]) {
    return data.filter((item) => {
        return item == 1;
    }).length;
}

export function pieGetTime(value: number) {
    let m = Math.floor(value / 60);
    let time = "";
    if (m > 0) {
        time = m + "分钟";
    } else {
        time = value + "秒";
    }
    return time;
}

export function pieGetPercentage(value: number, length: number) {
    let percentage = "0";
    if (value > 0) {
        percentage = Math.floor((value / length) * 100) + "%";
    } else {
        percentage = "0%";
    }
    return percentage;
}

export function formatTime(seconds: number) {
    let minutes = Math.floor(seconds / 60);
    let remainingSeconds = seconds % 60;
    return `${minutes}分钟${remainingSeconds}秒`;
}

function formatTimeRange(startTime: number, endTime: number) {
    let startFormatted = formatTime(startTime);
    let endFormatted = formatTime(endTime);
    return `${startFormatted} - ${endFormatted}`;
}

export function behaviorParseData2(data: any[], length: number) {
    let arr: any = [];
    data.forEach((item) => {
        let obj: any = {};
        let m = Math.floor(item.e / 60);
        let s = item.s % 60;
        let time = "";
        if (m < 1) {
            time = s + "s";
        } else {
            time = m + "m" + s + "s";
        }
        let _width = item.e - item.s ? item.e - item.s : 0;
        obj.time = time;
        obj.l = item.s / length;
        obj.w = _width / length;
        obj.s = item.s;
        obj.e = item.e;
        obj.left = (item.s / length) * 100 + "%";
        obj.width = (_width / length) * 100 + "%";
        obj.time2 = formatTimeRange(item.s, item.e);
        arr.push(obj);
    });
    return arr;
}

interface TimeRange {
  s: number; // 开始时间(秒)
  e: number; // 结束时间(秒)
}

export function getTimeRanges(arr: number[], target: number, timeUnit: number = 5): TimeRange[] {
    const result: TimeRange[] = [];

    for (let i = 0; i < arr.length; i++) {
        if (arr[i] === target) {
            const startTime = i * timeUnit;
            const endTime = startTime + timeUnit;
            result.push({
                s: startTime,
                e: endTime
            });
        }
    }

    return result;
}

export function papareList(data: number[], step: number) {
    if (data && data.length > 0) {
        const list = [];
        for (let index = 0; index < data.length; index += step) {
            list.push(data.slice(index, index + step));
        }
        return list.map((item) => {
            let num = 0;
            item.forEach((i) => {
                num += i;
            });
            return num;
        });
    } else {
        return [];
    }
}

export function getAnalysisStyle(value: string) {
    const arr = [
        {
            name: "促进深层次讲解",
            className: "lijie",
        },
        {
            name: "促进批判性思考",
            className: "sikao",
        },
        {
            name: "促进深度分析",
            className: "fenxi",
        },
        {
            name: "促进综合应用",
            className: "yingyong",
        },
    ];

    const result = arr.find((item) => {
        return item.name === value;
    });

    if (result) {
        return result.className;
    }
    return "lijie";
}
