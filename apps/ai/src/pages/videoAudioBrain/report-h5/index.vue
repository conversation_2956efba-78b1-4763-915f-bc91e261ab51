<script lang="ts" setup>
import { ref, watch, computed, provide, onUnmounted, nextTick } from "vue";
import { ysEmpt, ysIcon } from "@ys/ui";
import { marked } from "marked";
import { Markmap } from "markmap-view";
import { Transformer } from "markmap-lib";
import lineTitleH5 from "../components/lineTitleH5.vue";
import Line from "./echartsCom/line.vue";
import stuBehavior from "./echartsCom/stuBehavior.vue";
import teachBehavior from "./echartsCom/teachBehavior.vue";
import Pie1 from "./echartsCom/pie1.vue";
import Pie2 from "./echartsCom/pie2.vue";
import ctCurve from "./echartsCom/ctCurve.vue";
import rtch from "./echartsCom/rtch.vue";
import messageVior from "./message/index.vue";
import ktcInfo from "./ktcInfo/index.vue";
import keyword from "./keyword/index.vue";
import classroomStructure from "./classroomStructure/index.vue";
import boardAnalysis from "./boardAnalysis/index.vue";
import roomMessage from "./message/roomMessage.vue";
import bloom from "./bloom/index.vue";
import fourRader from "./fourRader/index.vue";
import execution from "./execution/index.vue";
import behaviorAnalysis from "./behaviorAnalysis/index.vue";
import behaviorRatio from "./behaviorRatio/index.vue";
import { common, CommonRes } from "@ys/tools";
import { useRoute } from "vue-router";
import {
    selectVisitEventInfo,
    queryAiTaskGroup,
    queryAiTaskGroupSmallClass,
} from "../api";
import {
    AiCaptionTask,
    AiClassActionData,
    AiClassActionDataVo,
    AiTaskGroupVo,
    MergedSegmentProps,
    Static,
    AiClassBehaviorData
} from "./../entity";
import icon1 from "./icon1.png";
import icon2 from "./icon2.png";
import icon3 from "./icon3.png";
import icon4 from "./icon4.png";
import icon5 from "./icon5.png";
import icon6 from "./icon6.png";
import icon7 from "./icon7.png";
import icon8 from "./icon8.png";
import icon9 from "./icon9.png";

const { getUrlQuery, createDefaultAvatar } = common;

const transformer = new Transformer();
const route = useRoute();
const id = route.params.id;

const resetLoading = ref(true);
const eventName = ref("");
const eventTalkManName = ref("");
const userFace = ref("");
const captionOpenSpeaker = ref(1);
const objType = ref<number>(1); // 1 活动 2 资源
const aiAuth = ref<number>(1); // 1 教师 2 学生
const classroomType = ref(2); // 1 普通 2 微格 3 电影
const groupId = ref("");
const hasResult = ref(false);
const aiTaskGroupVo = ref<AiTaskGroupVo>();
const aiClassStData = ref<any>({});
const aiCaptionTask = ref<AiCaptionTask>();
const roleChats = ref<any>([]);
const aiClassActionData = ref<AiClassActionData>();
const aiClassActionDataVo = ref<AiClassActionDataVo>();
const aiClassBehaviorData = ref<AiClassBehaviorData>();
const mergedSegments = ref<MergedSegmentProps[]>();
const aiClassBlackboardData = ref<any>();
const aiClassSpeakData = ref<any>();
const teachaerTotalMinutes = ref("0");
const studentTotalMinutes = ref("0");
const summarize = ref(""); //课程内容概括
const hapterOverview = ref(""); //章节总览
const testMe = ref(""); //考考我
let markmap: any; //思维导图
const contentSummary = ref(""); //内容总结
const lessonPlan = ref(""); // AI教案
const lessonPlanPropose = ref("");
const advantage = ref(""); //综述
const importsItuation = ref(""); //导入>技能应用情况
const importSuggestion = ref(""); //导入>评价建议
const explainItuation = ref(""); //讲解>技能应用情况
const explainSuggestion = ref(""); //讲解>评价建议
const quizItuation = ref(""); //提问>技能应用情况
const quizSuggestion = ref(""); //提问>评价建议
const changeItuation = ref(""); //变化>技能应用情况
const changeSuggestion = ref(""); //变化>评价建议
const strengItuation = ref(""); //强化>技能应用情况
const strengSuggestion = ref(""); //强化>评价建议
const endItuation = ref(""); //强化>技能应用情况
const endSuggestion = ref(""); //强化>评价建议
const fluency = ref("");
const teachaerAverageSpeed = ref("0");
const ktcWords = ref(0);
const methodList = ref<string[]>([]);

provide("classroomType", classroomType);
provide("aiAuth", aiAuth);
provide("objType", objType);
provide("captionOpenSpeaker", captionOpenSpeaker);
provide("aiClassActionData", aiClassActionData);
provide("aiClassActionDataVo", aiClassActionDataVo);
provide("aiClassBehaviorData", aiClassBehaviorData);
provide("mergedSegments", mergedSegments);
provide("aiClassBlackboardData", aiClassBlackboardData);
provide("aiClassSpeakData", aiClassSpeakData);
provide("aiClassStData", aiClassStData);
provide("roleChats", roleChats);

const isStuAll = computed(() => {
    if (
        aiClassActionDataVo?.value &&
        (aiClassActionDataVo?.value.listen.length > 0 ||
            aiClassActionDataVo?.value.recite.length > 0 ||
            aiClassActionDataVo?.value.reading.length > 0 ||
            aiClassActionDataVo?.value.writing.length > 0 ||
            aiClassActionDataVo?.value.discuss.length > 0 ||
            aiClassActionDataVo?.value.studentOther.length > 0)
    ) {
        return true;
    } else {
        return false;
    }
});
const isTeachAll = computed(() => {
    if (
        aiClassActionDataVo?.value &&
        (aiClassActionDataVo?.value.teach.length > 0 ||
            aiClassActionDataVo?.value.blackboardWriting.length > 0 ||
            aiClassActionDataVo?.value.makeInspectionTour.length > 0 ||
            aiClassActionDataVo?.value.onStageInteraction.length > 0 ||
            aiClassActionDataVo?.value.teacherOther.length > 0)
    ) {
        return true;
    } else {
        return false;
    }
});
const speakPercent = computed(() => {
    if (studentTotalMinutes.value === "0") {
        return "100%";
    }
    if (teachaerTotalMinutes.value === "0") {
        return "0";
    }
    const p =
        (Number(teachaerTotalMinutes.value) /
            (Number(studentTotalMinutes.value) +
                Number(teachaerTotalMinutes.value))) *
        100;

    return p.toFixed(2) + "%";
});
async function getEventDetailInfo() {
    if (!route.params.id || route.params.id === '0') return;
    resetLoading.value = false;
    const result = await selectVisitEventInfo<CommonRes<any>>({
        eventId: route.params.id,
    });
    if (result.data.code === 0) {
        eventTalkManName.value = result.data.data.eventInfo.eventTalkManName;
        userFace.value = result.data.data.eventInfo.userFace || "";
        eventName.value = result.data.data.eventInfo.eventName;
        captionOpenSpeaker.value =
            result.data.data.eventOtherInfo.captionOpenSpeaker;
        // document.title = title.value; // 设置页面标题
    } else {
        resetLoading.value = true;
    }
}
async function getInfo2() {
    if (!route.query.groupId) return;
    let result = null;
    if (classroomType?.value == 1 || classroomType?.value == 3) {
        result = await queryAiTaskGroup<CommonRes<any>>({
            groupId: route.query.groupId,
        });
    } else {
        result = await queryAiTaskGroupSmallClass<CommonRes<any>>({
            groupId: route.query.groupId,
        });
    }
    if (result.data.code == 0) {
        groupId.value = route.query.groupId as string;
        resetLoading.value = true;
        const data = result.data.data;
        hasResult.value = true;
        aiClassStData.value = data.aiClassStData;
        aiCaptionTask.value = data.aiCaptionTask;
        roleChats.value = data.roleChats;
        aiTaskGroupVo.value = data.aiTaskGroupVo;
        aiClassActionData.value = data.aiClassActionData;
        aiClassActionDataVo.value = data.aiClassActionDataVo;
        aiClassBehaviorData.value = data.aiClassBehaviorData;
        mergedSegments.value = data.mergedSegments;
        document.title = aiTaskGroupVo.value?.groupName || "影像大脑";
        aiClassBlackboardData.value = data.aiClassBlackboardData;
        aiClassSpeakData.value = data.aiClassSpeakData;

        getHtml(1);
        getHtml(2);
        nextTick(() => {
            initMarkmap();
        });
        getHtml(4);
        init18Mark(); //内容总结
        getReviewHtml(17);
        getReviewHtml(5);
        getReviewHtml(11);
        getReviewHtml(6);
        getReviewHtml(12);
        getReviewHtml(7);
        getReviewHtml(13);
        getReviewHtml(8);
        getReviewHtml(14);
        getReviewHtml(9);
        getReviewHtml(15);
        getReviewHtml(10);
        getReviewHtml(16);
        init23Mark(23);
        init23Mark(24);
    }
}

// 获取课程内容概括
async function getHtml(key: number) {
    const renderer = new marked.Renderer();
    // 重写 heading 方法
    renderer.heading = function (text, level) {
        // 如果是一级标题，将其改为三级标题
        if (level === 1) {
            level = 3;
        }
        if (level === 2) {
            level = 4;
        }
        // 返回修改后的标题
        return `<h${level}>${text}</h${level}>`;
    };
    // 使用自定义渲染器
    marked.setOptions({
        renderer: renderer,
    });
    const list = roleChats.value[1];
    const item = list.find((item: any) => item.questionType === key);
    if (!item) return;
    const html1 = await marked.parse(item!.answer, {
        breaks: true,
    });
    if (key === 1) {
        summarize.value = html1;
    } else if (key === 2) {
        hapterOverview.value = html1;
    } else if (key === 4) {
        testMe.value = html1;
    }
}
// 获取思维导图
const initMarkmap = async () => {
    markmap = Markmap?.create("#markmapTeachContent");
    const list = roleChats?.value[1];
    const mindValue = list.find((item: any) => item.questionType === 2);
    if (!mindValue) return;
    const { root } = await transformer.transform(mindValue.answer);
    markmap.setData(root);
    markmap.fit();
    return;
};
// 获取综述
async function getReviewHtml(key: number) {
    const list = roleChats.value[2];
    const item = list.find((item: any) => item.questionType === key);
    if (!item) return;
    const html1 = await marked.parse(item!.answer, {
        breaks: true,
    });
    if (key === 17) {
        advantage.value = html1;
    } else if (key === 5) {
        importsItuation.value = html1;
    } else if (key === 11) {
        importSuggestion.value = html1;
    } else if (key === 6) {
        explainItuation.value = html1;
    } else if (key === 12) {
        explainSuggestion.value = html1;
    } else if (key === 7) {
        quizItuation.value = html1;
    } else if (key === 13) {
        quizSuggestion.value = html1;
    } else if (key === 8) {
        changeItuation.value = html1;
    } else if (key === 14) {
        changeSuggestion.value = html1;
    } else if (key === 9) {
        strengItuation.value = html1;
    } else if (key === 15) {
        strengSuggestion.value = html1;
    } else if (key === 10) {
        endItuation.value = html1;
    } else if (key === 16) {
        endSuggestion.value = html1;
    }
}

// 获取内容总结
const init18Mark = async () => {
    const list = roleChats?.value[3];
    const find18 = list.find((item: any) => item.questionType === 18);
    let find18_answer = find18.answer;
    const str1 = await marked.parse(find18_answer, {
        breaks: true,
    });
    contentSummary.value = str1;
};
const init23Mark = async (key: number) => {
    const list = roleChats?.value[3];
    const find23 = list.find((item: any) => item.questionType === key);
    let find23_answer = find23.answer;
    const str1 = await marked.parse(find23_answer, {
        breaks: true,
    });
    if (key === 23) {
        lessonPlan.value = str1;
    } else if (key === 24) {
        lessonPlanPropose.value = str1;
    }
};

const modeValue = ref<number>(1);

watch(
    () => route.path,
    (val) => {
        objType.value = Number(route.params.objType);
        aiAuth.value = Number(route.params.aiAuth);
        const suffixId = getUrlQuery("suffixId");
        if (suffixId) {
            sessionStorage.setItem("suffixId", suffixId);
        }
        getEventDetailInfo();
    },
    { immediate: true }
);
watch(
    () => route.query,
    (val) => {
        classroomType.value = Number(route.query.taskType);
        getInfo2();
    },
    { immediate: true }
);

const trackLeft = computed(() => {
    const p = (Number(teachaerAverageSpeed.value) / 400) * 100;
    if (p > 100) return "100";
    return p.toFixed(1);
});

const paper = (val: Static) => {
    if (!val.speak) return;
    const speak = JSON.parse(val.speak);

    if (speak.teacher) {
        fluency.value = speak.teacher.fluency;
    } else if (speak.student) {
        fluency.value = speak.student.fluency;
    }

    if (speak.teacher) {
        teachaerAverageSpeed.value = speak.teacher.averageSpeed.toFixed(2);
        teachaerTotalMinutes.value = speak.teacher.totalMinutes.toFixed(2);
    } else {
        teachaerAverageSpeed.value = "0";
        teachaerTotalMinutes.value = "0";
    }
};

watch(
    () => aiClassStData?.value,
    (val) => {
        nextTick(() => {
            paper(val as Static);
            ktcWords.value = (val as Static).ktcWords;
        });
    },
    // { deep: true }
    { immediate: true }
);

const setMethodList = (value: any[]) => {
    let arr: string[] = [];
    value.forEach((item) => {
        if (item["教学方式"] != "无") {
            arr.push(item["教学方式"]);
        }
    });
    let arr2: any = [];
    arr.forEach((item) => {
        if (item.indexOf("/") != -1) {
            let obj_arr = item.split("/");
            obj_arr.forEach((item2) => {
                arr2.push(item2);
            });
        } else if (item.indexOf("、") != -1) {
            let obj_arr = item.split("、");
            obj_arr.forEach((item2) => {
                arr2.push(item2);
            });
        } else {
            arr2.push(item);
        }
    });
    arr2 = Array.from(new Set(arr2));
    methodList.value = arr2;
};

watch(
    () => roleChats?.value,
    (newV) => {
        if (newV) {
            const roleChat3 = newV[3] as any[];
            if (roleChat3) {
                const data19 = roleChat3.find(
                    (item) => item.questionType === 19
                );
                if (data19) {
                    const data19Answer = JSON.parse(data19.answer);
                    setMethodList(data19Answer);
                }

                const data27 = roleChat3.find(
                    (item) => item.questionType === 27
                );
                if (data27 && data27.answer) {
                    const data27Answer = JSON.parse(data27.answer);
                    onSetBloom(data27Answer);
                    onSetFourRader(data27Answer);
                }
            }
        }
    },
    { immediate: true }
);

const setMethodIcon = (val: string) => {
    if (val == "讲授") {
        return icon1;
    } else if (val == "自主学习") {
        return icon2;
    } else if (val == "问答") {
        return icon3;
    } else if (val == "讨论") {
        return icon4;
    } else if (val == "练习") {
        return icon5;
    } else if (val == "任务驱动") {
        return icon6;
    } else if (val == "参观教学") {
        return icon7;
    } else if (val == "现场教学") {
        return icon8;
    } else if (val == "自主学习") {
        return icon9;
    } else {
        return icon8;
    }
};

interface AnalysisQuestion {
    text: string;
    num: number;
    color: string;
    icon?: string;
    englishText?: string;
}

const bloomQuestionList = ref<AnalysisQuestion[]>([
    {
        icon: "icondengpao",
        text: "创造",
        num: 0,
        color: "#F1584E",
    },
    {
        icon: "iconpingfen",
        text: "评价",
        num: 0,
        color: "#F48A2E",
    },
    {
        icon: "iconshujutu",
        text: "分析",
        num: 0,
        color: "#FAC42D",
    },
    {
        icon: "iconfangkuai",
        text: "应用",
        num: 0,
        color: "#59BE7F",
    },
    {
        icon: "iconbiaoqing1",
        text: "理解",
        num: 0,
        color: "#4AB4D0",
    },
    {
        icon: "iconcengji1",
        text: "记忆",
        num: 0,
        color: "#8F7BD3",
    },
]);

const onSetBloom = (value: any[]) => {
    value.forEach((item) => {
        switch (item["布鲁姆"]) {
            case "创造":
                bloomQuestionList.value[0].num++;
                break;
            case "评价":
                bloomQuestionList.value[1].num++;
                break;
            case "分析":
                bloomQuestionList.value[2].num++;
                break;
            case "应用":
                bloomQuestionList.value[3].num++;
                break;
            case "理解":
                bloomQuestionList.value[4].num++;
                break;
            case "记忆":
                bloomQuestionList.value[5].num++;
                break;
            default:
                break;
        }
    });
};

const fourRaderQuestionList = ref<AnalysisQuestion[]>([
    {
        text: "若何",
        englishText: "WHAT IF",
        num: 0,
        color: "#ED9093",
    },
    {
        text: "如何",
        englishText: "HOW",
        num: 0,
        color: "#79CCB4",
    },
    {
        text: "为何",
        englishText: "WHY",
        num: 0,
        color: "#4F72EB",
    },
    {
        text: "是何",
        englishText: "WHAT",
        num: 0,
        color: "#917EE5",
    },
]);
const onSetFourRader = (value: any[]) => {
    value.forEach((item) => {
        switch (item["四何"]) {
            case "若何":
                fourRaderQuestionList.value[0].num++;
                break;
            case "如何":
                fourRaderQuestionList.value[1].num++;
                break;
            case "为何":
                fourRaderQuestionList.value[2].num++;
                break;
            case "是何":
                fourRaderQuestionList.value[3].num++;
                break;
            default:
                break;
        }
    });
};

onUnmounted(() => {
    document.title = "影像大脑";
});
</script>

<template>
    <div class="ai-brain-report">
        <div class="ai-brain-report-type">
            <img v-if="classroomType === 2" src="../assets/title1.png" alt="" />
            <img v-else src="../assets/title2.png" alt="" />
        </div>
        <div v-if="id && id !== '0'" class="ai-brain-report-title">
            <div class="event">
                <div class="event-phone">
                    <img
                        class="avatar"
                        :src="userFace || createDefaultAvatar()"
                    />
                </div>
                <div class="event-name">{{ eventTalkManName }}</div>
            </div>
            <div class="event-content">{{ eventName }}</div>
        </div>
        <!-- 微格教室分析 -->
        <div v-if="classroomType === 2">
            <!-- 板书分析 -->
            <lineTitleH5 title="板书分析" />
            <div class="behavior-container behavior">
                <boardAnalysis v-if="aiClassBlackboardData" />
            </div>
            <!-- 发言 -->
            <lineTitleH5 title="发言" />
            <div class="behavior-container behavior">
                <roomMessage v-if="aiClassSpeakData" />
            </div>
        </div>
        <!-- 课程分析 -->
        <div v-else>
            <!-- 教师发言情况 -->
            <lineTitleH5 title="教师发言情况" />
            <div class="behavior-container behavior">
                <div class="teacher-speech">
                    <div class="speech-item speech1">
                        <div class="title3">教师发言时长</div>
                        <div class="value">
                            <span class="num">{{ teachaerTotalMinutes }}</span>
                            <span class="text">分钟</span>
                        </div>
                    </div>
                    <div class="speech-item speech2">
                        <div class="title3">教师发言占比</div>
                        <div class="value">
                            <span class="num">{{ trackLeft }}</span>
                            <span class="text">%</span>
                        </div>
                    </div>
                    <div class="speech-item speech3">
                        <div class="title3">流利度</div>
                        <div class="value">
                            <span class="num">{{ fluency || '未知' }}</span>
                        </div>
                    </div>
                    <div class="speech-item speech4">
                        <div class="title3">口头禅</div>
                        <div class="value">
                            <span class="num">{{ ktcWords || 0 }}</span>
                            <span class="text">次</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 教学方法 -->
            <lineTitleH5 title="教学方法" />
            <div class="behavior-container behavior">
                <div
                    v-show="!methodList.length"
                    style="position: relative; height: 75px"
                >
                    <ysEmpt />
                </div>
                <div class="method">
                    <div class="method-item" v-for="item in methodList">
                        <img :src="setMethodIcon(item)" />
                        <span>{{ item }}</span>
                    </div>
                </div>
            </div>
            <!-- 布鲁姆问题分析 -->
            <lineTitleH5 title="布鲁姆问题分析" />
            <div class="behavior-container behavior">
                <div
                    v-show="!bloomQuestionList.length"
                    style="position: relative; height: 75px"
                >
                    <ysEmpt />
                </div>
                <div class="bloom">
                    <div
                        class="bloom-item"
                        v-for="(item, index) in bloomQuestionList"
                        :key="index"
                    >
                        <ys-icon
                            class="icon"
                            :type="item.icon as string"
                            :style="{ fontSize: '28px', color: item.color }"
                        />
                        <span style="margin-top: 12px">{{ item.text }}</span>
                        <span class="num">{{ item.num }}</span>
                    </div>
                </div>
            </div>

            <!-- “四何”问题分析 -->
            <lineTitleH5 title="“四何”问题分析" />
            <div class="behavior-container behavior">
                <div
                    v-show="!fourRaderQuestionList.length"
                    style="position: relative; height: 75px"
                >
                    <ysEmpt />
                </div>
                <div class="four-rader">
                    <div
                        class="four-rader-item"
                        v-for="(item, index) in fourRaderQuestionList"
                        :key="index"
                    >
                        <span class="english" :style="{ color: item.color }">{{
                            item.englishText
                        }}</span>
                        <span style="font-weight: bold">{{ item.text }}</span>
                        <span class="num">{{ item.num }}</span>
                    </div>
                </div>
            </div>

            <!-- 课堂行为 -->
            <lineTitleH5 title="课堂行为" />
            <template v-if="!aiClassBehaviorData">
                <div class="behavior-container behavior">
                    <div class="label">
                        <span>学生个人行为</span>
                        <img src="../assets/label_icon.png" alt="" />
                    </div>
                    <Line v-if="aiClassActionData" />
                    <div v-else style="position: relative; height: 180px">
                        <ysEmpt />
                    </div>
                </div>
                <!-- 学生群体行为 -->
                <div class="behavior-container behavior">
                    <div class="label">
                        <span>学生群体行为</span>
                        <img src="../assets/label_icon.png" alt="" />
                    </div>
                    <stuBehavior v-show="isStuAll" :isStuAll="isStuAll" />
                    <div
                        v-show="!isStuAll"
                        style="position: relative; height: 180px"
                    >
                        <ysEmpt />
                    </div>
                </div>
                <!-- 教师行为 -->
                <div class="behavior-container behavior">
                    <div class="label">
                        <span>教师行为</span>
                        <img src="../assets/label_icon.png" alt="" />
                    </div>
                    <teachBehavior v-show="isTeachAll" :isTeachAll="isTeachAll" />
                    <div
                        v-show="!isTeachAll"
                        style="position: relative; height: 180px"
                    >
                        <ysEmpt />
                    </div>
                </div>
                <!-- 学生群体行为占比 -->
                <div class="behavior-container behavior">
                    <div class="label">
                        <span>学生群体行为占比</span>
                        <img src="../assets/label_icon.png" alt="" />
                    </div>
                    <Pie1 />
                </div>
                <!-- 教师行为占比 -->
                <div class="behavior-container behavior">
                    <div class="label">
                        <span>教师行为占比</span>
                        <img src="../assets/label_icon.png" alt="" />
                    </div>
                    <Pie2 />
                </div>
            </template>

            <template v-else>
                <div class="behavior-container behavior">
                    <div class="label">
                        <span>行为分析</span>
                        <img src="../assets/label_icon.png" alt="" />
                    </div>
                    <behaviorAnalysis />
                    <!-- <div v-else style="position: relative; height: 180px">
                        <ysEmpt />
                    </div> -->
                </div>
                <div class="behavior-container behavior">
                    <div class="label">
                        <span>行为占比</span>
                        <img src="../assets/label_icon.png" alt="" />
                    </div>
                    <behaviorRatio />
                </div>
            </template>

            <!-- S-T分析 -->
            <lineTitleH5 title="S-T分析" />
            <div class="behavior-container behavior">
                <div class="label">
                    <span>S-T曲线图</span>
                    <img src="../assets/label_icon.png" alt="" />
                </div>
                <ctCurve :data="aiClassStData?.stData" />
            </div>
            <!-- Rt-Ch图 -->
            <div class="behavior-container behavior">
                <div class="label">
                    <span>Rt-Ch图</span>
                    <img src="../assets/label_icon.png" alt="" />
                </div>
                <div class="mode-selector">
                    <div
                        class="mode-item"
                        :class="{ active: modeValue === 1 }"
                        @click="modeValue = 1"
                    >
                        标准
                    </div>
                    <div
                        class="mode-item"
                        :class="{ active: modeValue === 2 }"
                        @click="modeValue = 2"
                    >
                        常模
                    </div>
                </div>
                <rtch
                    :rt="aiClassStData?.rtData"
                    :ch="aiClassStData?.chData"
                    :mode="modeValue"
                />
            </div>
            <!-- 发言 -->
            <lineTitleH5 title="发言" />
            <div class="behavior-container behavior">
                <messageVior v-if="aiClassStData" />
            </div>
        </div>
        <!-- 微格教室分析&课程分析共存的 -->
        <div>
            <!-- 口头禅 -->
            <lineTitleH5 title="口头禅" />
            <div class="behavior-container behavior">
                <ktcInfo
                    :data="
                        classroomType === 2
                            ? aiClassSpeakData?.speakSpeed
                            : aiClassStData?.speak
                    "
                />
            </div>
            <!-- 关键词 -->
            <lineTitleH5 title="关键词" />
            <div class="behavior-container behavior">
                <div style="height: 176px">
                    <keyword />
                </div>
            </div>
            <!-- 课程内容概括 -->
            <lineTitleH5 title="课程内容概括" />
            <div class="behavior-container behavior">
                <div v-html="summarize"></div>
            </div>
            <!-- 章节总览 -->
            <!-- <lineTitleH5 title="章节总览" />
            <div class="behavior-container behavior">
                <div v-html="hapterOverview"></div>
            </div> -->
            <!-- 思维导图 -->
            <lineTitleH5 title="思维导图" />
            <div class="behavior-container behavior">
                <svg id="markmapTeachContent" class="markmap-content"></svg>
            </div>
            <!-- 考考我 -->
            <!-- <lineTitleH5 title="考考我" />
            <div class="behavior-container behavior">
                <div v-html="testMe"></div>
            </div> -->
            <!-- 内容总结 -->
            <lineTitleH5 title="内容总结" />
            <div class="behavior-container behavior">
                <div v-html="contentSummary"></div>
            </div>
            <!-- 课堂结构 -->
            <lineTitleH5 title="课堂结构" />
            <div class="behavior-container behavior">
                <classroomStructure />
            </div>
            <!-- 布鲁姆分析 -->
            <lineTitleH5 title="布鲁姆分析" />
            <div class="behavior-container behavior">
                <bloom />
            </div>
            <!-- “四何”分析 -->
            <lineTitleH5 title="“四何”分析" />
            <div class="behavior-container behavior">
                <fourRader />
            </div>
            <!-- 教案执行度分析 -->
            <lineTitleH5 title="教案执行度分析" />
            <div class="behavior-container behavior">
                <execution />
            </div>
            <!-- AI教案 -->
            <lineTitleH5 title="AI教案" />
            <div class="behavior-container behavior">
                <div v-html="lessonPlan"></div>
                <lineTitleH5 title="优化&建议" style="font-size: 14px" />
                <div v-html="lessonPlanPropose"></div>
            </div>
            <!-- 综述 -->
            <div v-if="advantage">
                <lineTitleH5 title="综述" />
                <div class="behavior-container behavior">
                    <div v-html="advantage"></div>
                </div>
            </div>
            <!-- 导入 -->
            <div v-if="importsItuation || importSuggestion">
                <lineTitleH5 title="教学导入技能" />
                <div class="behavior-container behavior">
                    <div style="margin-bottom: 16px" v-if="importsItuation">
                        <lineTitleH5
                            title="导入技能应用情况"
                            style="font-size: 14px"
                        />
                        <div v-html="importsItuation"></div>
                    </div>
                    <div style="margin-bottom: 16px" v-if="importSuggestion">
                        <lineTitleH5
                            title="评价&建议"
                            style="font-size: 14px"
                        />
                        <div v-html="importSuggestion"></div>
                    </div>
                </div>
            </div>
            <!-- 讲解 -->
            <div v-if="explainItuation || explainSuggestion">
                <lineTitleH5 title="教学讲解技能" />
                <div class="behavior-container behavior">
                    <div style="margin-bottom: 16px" v-if="explainItuation">
                        <lineTitleH5
                            title="讲解技能应用情况"
                            style="font-size: 14px"
                        />
                        <div v-html="explainItuation"></div>
                    </div>
                    <div style="margin-bottom: 16px" v-if="explainSuggestion">
                        <lineTitleH5
                            title="评价&建议"
                            style="font-size: 14px"
                        />
                        <div v-html="explainSuggestion"></div>
                    </div>
                </div>
            </div>
            <!-- 提问 -->
            <div v-if="quizItuation || quizSuggestion">
                <lineTitleH5 title="教学提问技能" />
                <div class="behavior-container behavior">
                    <div style="margin-bottom: 16px" v-if="quizItuation">
                        <lineTitleH5
                            title="提问技能应用情况"
                            style="font-size: 14px"
                        />
                        <div v-html="quizItuation"></div>
                    </div>
                    <div style="margin-bottom: 16px" v-if="quizSuggestion">
                        <lineTitleH5
                            title="评价&建议"
                            style="font-size: 14px"
                        />
                        <div v-html="quizSuggestion"></div>
                    </div>
                </div>
            </div>
            <!-- 变化 -->
            <div v-if="changeItuation || changeSuggestion">
                <lineTitleH5 title="教学变化技能" />
                <div class="behavior-container behavior">
                    <div style="margin-bottom: 16px" v-if="changeItuation">
                        <lineTitleH5
                            title="变化技能应用情况"
                            style="font-size: 14px"
                        />
                        <div v-html="changeItuation"></div>
                    </div>
                    <div style="margin-bottom: 16px" v-if="changeSuggestion">
                        <lineTitleH5
                            title="评价&建议"
                            style="font-size: 14px"
                        />
                        <div v-html="changeSuggestion"></div>
                    </div>
                </div>
            </div>
            <!-- 强化 -->
            <div v-if="strengItuation || strengSuggestion">
                <lineTitleH5 title="强化" />
                <div class="behavior-container behavior">
                    <div style="margin-bottom: 16px" v-if="strengItuation">
                        <lineTitleH5
                            title="技能应用情况"
                            style="font-size: 14px"
                        />
                        <div v-html="strengItuation"></div>
                    </div>
                    <div style="margin-bottom: 16px" v-if="strengSuggestion">
                        <lineTitleH5
                            title="评价&建议"
                            style="font-size: 14px"
                        />
                        <div v-html="strengSuggestion"></div>
                    </div>
                </div>
            </div>
            <!-- 结束 -->
            <div v-if="endItuation || endSuggestion">
                <lineTitleH5 title="结束" />
                <div class="behavior-container behavior">
                    <div style="margin-bottom: 16px" v-if="endItuation">
                        <lineTitleH5
                            title="技能应用情况"
                            style="font-size: 14px"
                        />
                        <div v-html="endItuation"></div>
                    </div>
                    <div style="margin-bottom: 16px" v-if="endSuggestion">
                        <lineTitleH5
                            title="评价&建议"
                            style="font-size: 14px"
                        />
                        <div v-html="endSuggestion"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="loading_box" v-if="!resetLoading">
        <a-spin tip="加载中..." />
    </div>
</template>

<style lang="scss" scoped>
@font-face {
    font-family: "YouSheBiaoTiHei";
    src: url("@/assets/fonts/YouSheBiaoTiHei.TTF") format("truetype");
    font-weight: normal;
    font-style: normal;
}
.ai-brain-report {
    min-width: 100vw;
    min-height: 100vh;
    background: url("../assets/Bg.png") no-repeat;
    background-size: 100%;
    background-color: #f2f2f6;
    padding: 12px;
    .ai-brain-report-type {
        width: 100%;
        height: 110px;
        img {
            width: 100%;
            height: 100%;
        }
    }
    .ai-brain-report-title {
        width: 100%;
        padding: 16px 28px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.6);
        border: 1px solid #fff;
        margin-bottom: 24px;
        .event {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            .event-phone {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
                overflow: hidden;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
            .event-nam {
                font-weight: 500;
                font-size: 16px;
                color: #262626;
            }
        }
        .event-content {
            font-weight: 500;
            font-size: 16px;
            color: #262626;
            line-height: 24px;
        }
    }
    .behavior {
        margin-bottom: 12px;
        .label {
            position: relative;
            span {
                font-weight: 600;
                font-size: 14px;
                color: #262626;
                line-height: 20px;
            }
            img {
                position: absolute;
                bottom: 0;
                left: 0;
                opacity: 0.7;
            }
        }
    }
    .behavior-container {
        position: relative;
        width: 100%;
        background-color: #fff;
        border-radius: 12px;
        padding: 20px 16px;
    }
    .markmap-content {
        width: 100%;
        padding: 24px 0;
    }
}
.loading_box {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
}

.mode-selector {
    position: absolute;
    top: 12px;
    right: 20px;
    display: inline-flex;
    background-color: #f0f0f0;
    border-radius: 8px;
    padding: 2px;

    .mode-item {
        padding: 5px 12px;
        cursor: pointer;
        border-radius: 8px;
        transition: all 0.2s ease;
        color: #333;

        &:hover {
            background-color: #e5e6e8;
        }

        &.active {
            background-color: #fff;
            font-weight: bold;
        }
    }
}

.teacher-speech {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    width: 100%;
    .speech-item {
        width: 48%;
        height: 86px;
        padding: 16px 20px;
        box-sizing: border-box;
        &.speech1 {
            background: #fff5f4;
            .title3 {
                color: #e95e5e;
            }
            .value {
                .num {
                    color: #e95e5e;
                }
                .text {
                    color: #e95e5e;
                }
            }
        }
        &.speech2 {
            background: #fff9f1;
            .title3 {
                color: #f9750e;
            }
            .value {
                .num {
                    color: #f9750e;
                }
                .text {
                    color: #f9750e;
                }
            }
        }
        &.speech3 {
            background: #f1fff9;
            margin-top: 10px;
            .title3 {
                color: #35c68b;
            }
            .value {
                .num {
                    color: #35c68b;
                }
                .text {
                    color: #35c68b;
                }
            }
        }
        &.speech4 {
            background: #f3f6ff;
            margin-top: 10px;
            .title3 {
                color: #006bfa;
            }
            .value {
                .num {
                    color: #006bfa;
                }
                .text {
                    color: #006bfa;
                }
            }
        }
        .value {
            display: flex;
            align-items: center;
            .num {
                font-size: 20px;
                font-weight: bold;
            }
            .text {
                margin-left: 6px;
            }
        }
    }
}
.method {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    flex-wrap: wrap;
    row-gap: 20px;
    column-gap: 5%;
    width: 100%;
    .method-item {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 0 0 30%;
        height: 56px;
        box-sizing: border-box;
        background: #f8f8f8;
        border-radius: 4px;
        img {
            margin-top: 4px;
            height: 42px;
        }
        span {
            color: #262626;
        }
    }
}
.bloom {
    display: flex;
    row-gap: 20px;
    column-gap: 5%;
    flex-wrap: wrap;
    &-item {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 30%;
        height: 103px;
        border-radius: 4px;
        border: 1px solid;
        &:first-child {
            background: linear-gradient(
                180deg,
                rgba(241, 88, 78, 0.1) 0%,
                rgba(255, 255, 255, 0.1) 100%
            );
            border-image: linear-gradient(
                    180deg,
                    rgba(241, 88, 78, 0.1),
                    rgba(241, 88, 78, 0.05)
                )
                1 1;
        }
        &:nth-child(2) {
            background: linear-gradient(
                180deg,
                rgba(244, 138, 46, 0.1) 0%,
                rgba(255, 255, 255, 0.1) 100%
            );
            border-image: linear-gradient(
                    180deg,
                    rgba(244, 138, 46, 0.1),
                    rgba(244, 138, 46, 0.05)
                )
                1 1;
        }
        &:nth-child(3) {
            background: linear-gradient(
                180deg,
                rgba(250, 205, 11, 0.1) 0%,
                rgba(255, 255, 255, 0.1) 100%
            );
            border-image: linear-gradient(
                    180deg,
                    rgba(250, 205, 11, 0.1),
                    rgba(250, 205, 11, 0.05)
                )
                1 1;
        }
        &:nth-child(4) {
            background: linear-gradient(
                180deg,
                rgba(89, 190, 127, 0.1) 0%,
                rgba(255, 255, 255, 0.1) 100%
            );
            border-image: linear-gradient(
                    180deg,
                    rgba(89, 190, 127, 0.1),
                    rgba(89, 190, 127, 0.05)
                )
                1 1;
        }
        &:nth-child(5) {
            background: linear-gradient(
                180deg,
                rgba(74, 180, 208, 0.1) 0%,
                rgba(255, 255, 255, 0.1) 100%
            );
            border-image: linear-gradient(
                    180deg,
                    rgba(74, 180, 208, 0.1),
                    rgba(74, 180, 208, 0.05)
                )
                1 1;
        }
        &:last-child {
            background: linear-gradient(
                180deg,
                rgba(143, 123, 211, 0.1) 0%,
                rgba(255, 255, 255, 0.1) 100%
            );
            border-image: linear-gradient(
                    180deg,
                    rgba(143, 123, 211, 0.1),
                    rgba(143, 123, 211, 0.05)
                )
                1 1;
        }
        .num {
            position: absolute;
            top: -10px;
            right: -10px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 13px;
            color: #007aff;
            width: 20px;
            height: 20px;
            line-height: 20px;
            background-color: #e6f6ff;
            border-radius: 50%;
        }
    }
}
.four-rader {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    row-gap: 20px;
    &-item {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 48%;
        height: 56px;
        padding: 0 20px;
        border-radius: 4px;
        background-color: #f8f8f8;
        &:first-child {
            .num {
                background: linear-gradient(180deg, #476ae1 0%, #7892f2 100%);
                &::after {
                    content: "";
                    position: absolute;
                    top: 0;
                    right: -5px;
                    width: 0;
                    height: 0;
                    border-left: 5px solid #2249af;
                    border-top: 5px solid transparent;
                }
            }
        }
        &:nth-child(2) {
            .num {
                background: linear-gradient(180deg, #62c2a6 0%, #96d8c6 100%);
                &::after {
                    content: "";
                    position: absolute;
                    top: 0;
                    right: -5px;
                    width: 0;
                    height: 0;
                    border-left: 5px solid #59a590;
                    border-top: 5px solid transparent;
                }
            }
        }
        &:nth-child(3) {
            .num {
                background: linear-gradient(180deg, #7e69dd 0%, #aa9aef 100%);
                &::after {
                    content: "";
                    position: absolute;
                    top: 0;
                    right: -5px;
                    width: 0;
                    height: 0;
                    border-left: 5px solid #403287;
                    border-top: 5px solid transparent;
                }
            }
        }
        &:last-child {
            .num {
                background: linear-gradient(180deg, #ea7c82 0%, #f1a8a8 100%);
                &::after {
                    content: "";
                    position: absolute;
                    top: 0;
                    right: -5px;
                    width: 0;
                    height: 0;
                    border-left: 5px solid #9d3d3f;
                    border-top: 5px solid transparent;
                }
            }
        }
        .english {
            font-family: YouSheBiaoTiHei;
        }
        .num {
            position: absolute;
            top: -5px;
            left: 8px;
            font-size: 13px;
            font-weight: bold;
            color: #fff;
            width: 22px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 0px 0px 4px 4px;
        }
    }
}
</style>
