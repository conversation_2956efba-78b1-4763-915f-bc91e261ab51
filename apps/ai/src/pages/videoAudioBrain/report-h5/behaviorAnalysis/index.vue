<template>
  <section class="behavior-analysis">
    <div class="left-label">
      <ul class="label-list">
        <li class="label-item">
          <div class="rect engagementScores"></div>
          <span class="text">活跃度</span>
        </li>
        <li class="label-item" style="margin-bottom: 30px;">
          <div class="rect focusScores"></div>
          <span class="text">专注度</span>
        </li>
        <li class="label-item">
          <div class="rect teach"></div>
          <span class="text">教师讲授</span>
        </li>
        <li class="label-item">
          <div class="rect blackboardWriting"></div>
          <span class="text">教师板书</span>
        </li>
        <li class="label-item">
          <div class="rect onStageInteraction"></div>
          <span class="text">师生互动</span>
        </li>
        <li class="label-item">
          <div class="rect groupStudy"></div>
          <span class="text">小组学习</span>
        </li>
        <li class="label-item">
          <div class="rect independentStudy"></div>
          <span class="text">独立学习</span>
        </li>
        <li class="label-item">
          <div class="rect studentShow"></div>
          <span class="text">学生展示</span>
        </li>
        <li class="label-item">
          <div class="rect studentHand"></div>
          <span class="text">学生举手</span>
        </li>
        <li class="label-item">
          <div class="rect teacherOther"></div>
          <span class="text">其他</span>
        </li>
      </ul>
    </div>
    <div class="right-box">
      <ul class="进度">
        <li>
          <div id="engagementFocusChart" class="chart" style="width: 100%; height: 85px;"></div>
        </li>
        <li class="box">
          <a-popover
            v-for="item in _teach"
            placement="top"
            overlayClassName="behavior-popover"
          >
            <template #content>
              <div>{{ item.time2 }}</div>
            </template>
            <div
              class="box-item teach"
              :title="item.time2"
              :style="{
                width: item.width,
                left: item.left,
              }"
            ></div>
          </a-popover>
        </li>

        <li class="box">
          <a-popover
            v-for="item in _blackboardWriting"
            placement="top"
            overlayClassName="behavior-popover"
          >
            <template #content>
              <div>{{ item.time2 }}</div>
            </template>
            <div
              class="box-item blackboardWriting"
              :title="item.time2"
              :style="{
                width: item.width,
                left: item.left,
              }"
            ></div>
          </a-popover>
        </li>

        <li class="box">
          <a-popover
            v-for="item in _onStageInteraction"
            placement="top"
            overlayClassName="behavior-popover"
          >
            <template #content>
              <div>{{ item.time2 }}</div>
            </template>
            <div
              class="box-item onStageInteraction"
              :title="item.time2"
              :style="{
                width: item.width,
                left: item.left,
              }"
            ></div>
          </a-popover>
        </li>

        <li class="box">
          <a-popover
            v-for="item in _groupStudy"
            placement="top"
            overlayClassName="behavior-popover"
          >
            <template #content>
              <div>{{ item.time2 }}</div>
            </template>
            <div
              class="box-item groupStudy"
              :title="item.time2"
              :style="{
                width: item.width,
                left: item.left,
              }"
            ></div>
          </a-popover>
        </li>

        <li class="box">
          <a-popover
            v-for="item in _independentStudy"
            placement="top"
            overlayClassName="behavior-popover"
          >
            <template #content>
              <div>{{ item.time2 }}</div>
            </template>
            <div
              class="box-item independentStudy"
              :title="item.time2"
              :style="{
                width: item.width,
                left: item.left,
              }"
            ></div>
          </a-popover>
        </li>

        <li class="box">
          <a-popover
            v-for="item in _studentShow"
            placement="top"
            overlayClassName="behavior-popover"
          >
            <template #content>
              <div>{{ item.time2 }}</div>
            </template>
            <div
              class="box-item studentShow"
              :title="item.time2"
              :style="{
                width: item.width,
                left: item.left,
              }"
            ></div>
          </a-popover>
        </li>

        <li class="box">
          <a-popover
            v-for="item in _studentHand"
            placement="top"
            overlayClassName="behavior-popover"
          >
            <template #content>
              <div>{{ item.time2 }}</div>
            </template>
            <div
              class="box-item studentHand"
              :title="item.time2"
              :style="{
                width: item.width,
                left: item.left,
              }"
            ></div>
          </a-popover>
        </li>

        <li class="box">
          <a-popover
            v-for="item in _teacherOther"
            placement="top"
            overlayClassName="behavior-popover"
          >
            <template #content>
              <div>{{ item.time2 }}</div>
            </template>
            <div
              class="box-item teacherOther"
              :title="item.time2"
              :style="{
                width: item.width,
                left: item.left,
              }"
            ></div>
          </a-popover>
        </li>

        <li v-if="lineNumber" class="line"></li>
        <li v-if="lineNumber" class="line-number">
          <span v-for="item in lineNumber" :key="item" class="line-number-item">{{ item }}</span>
        </li>
      </ul>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, Ref, watch, onBeforeUnmount, computed } from "vue";
import { behaviorParseData2, AiClassBehaviorData, getTimeRanges } from "../../entity";
import * as echarts from "echarts";

const aiClassBehaviorData = inject<Ref<AiClassBehaviorData>>("aiClassBehaviorData");

const _teach = ref<any[]>([]);
const _blackboardWriting = ref<any[]>([]);
const _onStageInteraction = ref<any[]>([]); // 师生互动
const _groupStudy = ref<any[]>([]); // 小组学习
const _independentStudy = ref<any[]>([]); // 独立学习
const _studentShow = ref<any[]>([]); // 学生展示
const _studentHand = ref<any[]>([]); // 学生举手
const _teacherOther = ref<any[]>([]); // 教师其他
const engagementData = ref<number[]>([]);
const focusData = ref<number[]>([]);
const chartInstance = ref<echarts.ECharts | null>(null);

const init = () => {
  if (aiClassBehaviorData?.value) {
    const engagementScores = JSON.parse(aiClassBehaviorData.value.engagementScores);
    const focusScores = JSON.parse(aiClassBehaviorData.value.focusScores);
    if (engagementScores.length > 0 && focusScores.length > 0) {
      engagementData.value = engagementScores;
      focusData.value = focusScores;
      setTimeout(() => {
        initEngagementFocusChart();
      }, 0);
    }
    const behavior = JSON.parse(aiClassBehaviorData.value.behavior);
    if (behavior.length > 0) {
      const dataLength = behavior.length * 5;
      _teach.value = behaviorParseData2(
        getTimeRanges(behavior, 1),
        dataLength
      );
      _blackboardWriting.value = behaviorParseData2(
        getTimeRanges(behavior, 2),
        dataLength
      );
      _onStageInteraction.value = behaviorParseData2(
        getTimeRanges(behavior, 3),
        dataLength
      );
      _groupStudy.value = behaviorParseData2(
        getTimeRanges(behavior, 4),
        dataLength
      );
      _independentStudy.value = behaviorParseData2(
        getTimeRanges(behavior, 5),
        dataLength
      );
      _studentShow.value = behaviorParseData2(
        getTimeRanges(behavior, 6),
        dataLength
      );
      _studentHand.value = behaviorParseData2(
        getTimeRanges(behavior, 7),
        dataLength
      );
      _teacherOther.value = behaviorParseData2(
        getTimeRanges(behavior, 8),
        dataLength
      );
    }
  }
};

const initEngagementFocusChart = () => {
  if (!engagementData.value.length || !focusData.value.length) return;

  const chartDom = document.getElementById("engagementFocusChart");
  if (!chartDom) return;

  // 销毁旧实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }

  chartInstance.value = echarts.init(chartDom);

  const option = {
    grid: {
      left: '0',
      right: '0',
      bottom: '0',
      top: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 10,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    series: [
      {
        name: '流畅度',
        type: 'line',
        data: engagementData.value,
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: '#007AFF'
        }
      },
      {
        name: '专注度',
        type: 'line',
        data: focusData.value,
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: '#17BE6B'
        }
      }
    ]
  };

  chartInstance.value.setOption(option);
};

const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};

const splitIntoSegments = (num: number): number[] => {
  const result: number[] = [];
  for (let i = 0; i <= 5; i++) {
    result.push(Math.floor(num * i / 5));
  }
  return result;
}

const lineNumber = computed(() => {
  if (aiClassBehaviorData?.value && aiClassBehaviorData.value.behavior) {
    const behavior = JSON.parse(aiClassBehaviorData.value.behavior);
    const dataLength = Math.floor(behavior.length * 5 / 60);
    return splitIntoSegments(dataLength);
  }
  return 0;
});

onMounted(() => {
  init();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

watch(
  () => aiClassBehaviorData?.value,
  (val) => {
    if (val) {
      init();
    }
  },
);
</script>

<style lang="scss" scoped>
.behavior-analysis {
  display: flex;
  width: 100%;
  padding: 20px 0 30px;
  box-sizing: border-box;
  .left-label {
    flex: 0 0 88px;
    width: 80px;
    .label-list {
      margin: 0;
      padding: 0;
      list-style: none;
      li {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        &:last-child {
          margin-bottom: 0;
        }
        .rect {
          width: 12px;
          height: 12px;
          border-radius: 2px 2px 2px 2px;
          &.engagementScores {
            background: #007AFF;
          }
          &.focusScores {
            background: #17BE6B;
          }
          &.teach {
            background: #5B8FF9;
          }
          &.blackboardWriting {
            background: #61DDAA;
          }
          &.onStageInteraction {
            background: #65789B;
          }
          &.groupStudy {
            background: #F6BD16;
          }
          &.independentStudy {
            background: #F56E53;
          }
          &.studentShow {
            background: #78D3F8;
          }
          &.studentHand {
            background: #14C9C9;
          }
          &.teacherOther {
            background: #7262FD;
          }
        }
        .text {
          margin-left: 8px;
          color: #262626;
        }
      }
    }
  }
  .right-box {
    position: relative;
    margin-left: 24px;
    /* width: 616px; */
    width: 100%;
    .mask {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      display: flex;
      .dashed {
        opacity: 0;
        position: absolute;
        top: 0;
        bottom: 0;
        width: 1px;
        height: 158px;
        background: #e5e5e5;
        cursor: pointer;
        &:hover {
          opacity: 1;
        }
      }
    }
    .进度 {
      margin: 0;
      padding: 0;
      list-style: none;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
}

.box {
  margin-bottom: 12px;
  position: relative;
  width: 100%;
  height: 22px;
  background: #f5f5f5;
  cursor: pointer;
  &:last-child {
    margin-bottom: 0;
  }
  .box-item {
    position: absolute;
    top: 0;
    height: 22px;
    &.teach {
      background: #5B8FF9;
    }
    &.blackboardWriting {
      background: #61DDAA;
    }
    &.onStageInteraction {
      background: #65789B;
    }
    &.groupStudy {
      background: #F6BD16;
    }
    &.independentStudy {
      background: #F56E53;
    }
    &.studentShow {
      background: #78D3F8;
    }
    &.studentHand {
      background: #14C9C9;
    }
    &.teacherOther {
      background: #7262FD;
    }
  }
}

.line {
  position: relative;
  width: 100%;
  height: 2px;
  background: #E5E5E5;
  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-left: 4px solid #999;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
  }
}
.line-number {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}
</style>

<style lang="scss">
.behavior-popover {
  .ant-popover-arrow {
    display: none;
  }
}
</style>
