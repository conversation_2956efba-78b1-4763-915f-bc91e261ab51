<script setup lang="ts">
import { inject, Ref, computed } from "vue";
import { ysEmpt } from "@ys/ui";
import Line from "./line.vue";
import pie1 from "./pie1.vue";
import pie2 from "./pie2.vue";
import speech from "./speech.vue";
import stuBehavior from "./stu-behavior.vue";
import teachBehavior from "./teach-behavior.vue";
import ktc from "./ktc.vue";
import charTabContainerWordCloud from "./wordCloud.vue";
import behaviorAnalysis from "../../portContainer/behaviorAnalysis.vue";
import behaviorRatio from "../../portContainer/behaviorRatio.vue";
import { AiClassActionDataVo, Static, AiClassBehaviorData } from "../../../entity";

const aiClassActionDataVo = inject<Ref<AiClassActionDataVo>>(
  "aiClassActionDataVo"
);
const aiClassStData = inject<Ref<Static>>("aiClassStData");
const aiClassBehaviorData = inject<Ref<AiClassBehaviorData>>("aiClassBehaviorData");

const isStuAll = computed(() => {
  if (
    aiClassActionDataVo?.value &&
    (aiClassActionDataVo?.value.listen.length > 0 ||
      aiClassActionDataVo?.value.recite.length > 0 ||
      aiClassActionDataVo?.value.reading.length > 0 ||
      aiClassActionDataVo?.value.writing.length > 0 ||
      aiClassActionDataVo?.value.discuss.length > 0 ||
      aiClassActionDataVo?.value.studentOther.length > 0)
  ) {
    return true;
  } else {
    return false;
  }
});

const isTeachAll = computed(() => {
  if (
    aiClassActionDataVo?.value &&
    (aiClassActionDataVo?.value.teach.length > 0 ||
      aiClassActionDataVo?.value.blackboardWriting.length > 0 ||
      aiClassActionDataVo?.value.makeInspectionTour.length > 0 ||
      aiClassActionDataVo?.value.onStageInteraction.length > 0 ||
      aiClassActionDataVo?.value.teacherOther.length > 0)
  ) {
    return true;
  } else {
    return false;
  }
});
</script>

<template>
  <section class="behavior">
    <template v-if="!aiClassBehaviorData">
      <Line class="export-img" data-type="studentPersonalBehavior" />

      <stuBehavior class="export-img" data-type="studentGroupBehavior" v-show="isStuAll" :isStuAll="isStuAll" />
      <div v-show="!isStuAll" style="position: relative; height: 180px">
        <ysEmpt />
      </div>

      <teachBehavior class="export-img" data-type="teacherBehavior" v-show="isTeachAll" :isTeachAll="isTeachAll" />
      <div v-show="!isTeachAll" style="position: relative; height: 180px">
        <ysEmpt />
      </div>

      <section class="export-img bing" data-type="teacherAndStudentBehaviorMix">
        <div class="bing-item">
          <pie1 />
        </div>
        <div class="bing-item">
          <pie2 />
        </div>
      </section>
    </template>

    <template v-else>
      <behaviorAnalysis class="export-img" style="padding-bottom: 50px;" data-type="behaviorAnalysis" />

      <behaviorRatio class="export-img" data-type="behaviorProportion" />
    </template>

    <speech />

    <div v-if="aiClassStData?.speak" class="export-img" data-type="petPhrase" style="height: 194px">
      <ktc />
    </div>
    <div v-else style="height: 194px; position: relative">
      <ysEmpt />
    </div>

    <div v-if="aiClassStData?.word" style="height: 194px;margin-top: 80px;" class="export-img" data-type="classroomKeywords">
      <charTabContainerWordCloud />
    </div>
    <div v-else style="height: 194px; position: relative">
      <ysEmpt />
    </div>
  </section>
</template>

<style lang="scss" scoped>
.title {
  display: flex;
  align-items: center;
  margin-top: 20px;
  width: 100%;
  height: 32px;
  background: #ecf5ff;
  border-radius: 0px 0px 0px 0px;
  .line {
    width: 4px;
    height: 16px;
    background: #007aff;
  }
  h3 {
    margin: 0;
    margin-left: 8px;
    font-size: 14px;
    color: #262626;
  }
}
.bing {
  margin: 0;
  padding: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  list-style: none;
  .bing-item {
    width: 49%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
</style>
