<script setup lang="ts">
import { inject, Ref } from "vue";
import { AiTaskGroupVo } from "../../../entity";
import { ysIcon } from "@ys/ui";

const aiTaskGroupVo = inject<Ref<AiTaskGroupVo>>("aiTaskGroupVo");
const subjectName = inject<Ref<AiTaskGroupVo>>("subjectName");

const getDate = (timestamp?: number) => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${year}-${month}-${day}`;
}
</script>

<template>
  <div class="export-img report-cover" data-type="cover">
    <img src="./report_cover.png" alt="" class="report-cover-img" crossorigin="anonymous">
    <div class="title">AI课程分析报告</div>
    <div class="info">
      <div class="info-item">
        <div class="info-title">课程信息</div>
        <div class="info-text">
          <div class="text">名 称：{{ aiTaskGroupVo?.groupName }}</div>
          <div class="text">时 间：{{ getDate(aiTaskGroupVo?.sourceTime) }}</div>
          <div class="text">主讲人：{{ aiTaskGroupVo?.lecturerName }}</div>
          <div class="text">学科：{{ subjectName }}</div>
        </div>
      </div>
      <div class="info-item">
        <div class="info-title">分析内容</div>
        <div class="info-img">
          <div class="info-img-item">
            <img src="./icon1.png" alt="">
            <p>课堂行为</p>
          </div>
          <div class="info-img-item">
            <img src="./icon2.png" alt="">
            <p>教学内容</p>
          </div>
          <div class="info-img-item">
            <img src="./icon3.png" alt="">
            <p>教研分析</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.report-cover {
  position: relative;
  height: 1123px;
  .report-cover-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: -1;
  }
  .title {
    display: flex;
    align-items: flex-end;
    font-size: 60px;
    font-weight: bold;
    height: 771px;
    // background: url(report_cover.png) no-repeat;
    // background-size: cover;
  }
  .info {
    display: flex;
    margin-top: 56px;
    .info-item {
      width: 50%;
      .info-title {
        display: flex;
        align-items: center;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 40px;
        &::before {
          content: "";
          width: 6px;
          height: 20px;
          display: inline-block;
          margin-right: 4px;
          background: linear-gradient( 90deg, #007AFF 0%, rgba(0,122,255,0.7) 100%);
        }
      }
      .info-text {
        .text {
          font-size: 16px;
          line-height: 24px;
          margin-bottom: 20px;
        }
      }
      .info-img {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 180px;
        padding: 0;
        background: linear-gradient( 90deg, #007AFF 0%, rgba(0,122,255,0.7) 100%);
        list-style: none;
        color: white;
        &-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          img {
            width: 34px;
            height: 34px;
          }
          >p {
            font-size: 16px;
            margin-top: 20px;
          }
        }
      }
    }
  }
}
</style>
