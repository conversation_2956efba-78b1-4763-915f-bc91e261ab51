<script lang="ts" setup>
import {
  ref,
  onMounted,
  onUnmounted,
  watch,
  inject,
  Ref,
  computed,
  nextTick,
} from "vue";
import * as echarts from "echarts";
import { Static } from "./../../../entity";

const aiClassStData = inject<Ref<Static>>("aiClassStData");

let chart: any = null;
function start(data: number[]) {
  if (!chart) {
    chart = echarts.init(document.getElementById("speechChart")!);
  }
  chart.setOption({
    tooltip: {
      trigger: "axis",
      formatter: function (params: any) {
        let tooltipContent = params
          .map((item: any) => {
            return `${item.marker} ${item.value.toFixed(2)}`;
          })
          .join("<br/>");

        return tooltipContent;
      },
    },
    grid: {
      left: 30,
      top: 10,
      bottom: 4,
      right: 10,
    },
    xAxis: {
      show: false,
      type: "category",
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          type: "dashed",
        },
      },
    },
    series: [
      {
        data,
        type: "line",
        smooth: true,
        itemStyle: {
          color: "#5B8FF9",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "#CEDDFD", // 起始颜色
            },
            {
              offset: 1,
              color: "#fff", // 结束颜色
            },
          ]),
        },
        emphasis: {
          disabled: true,
        },
      },
    ],
  });
}
onMounted(() => {
  if (aiClassStData?.value) {
    window.addEventListener("resize", function () {
      chart && chart.resize();
    });
  }
});
onUnmounted(() => {
  window.removeEventListener("resize", function () {
    chart && chart.resize();
  });
  if (chart) {
    chart.dispose();
    chart = null;
  }
});
const teachaerAverageSpeed = ref("0");
const teachaerTotalMinutes = ref("0");
const studentAverageSpeed = ref("0");
const studentTotalMinutes = ref("0");
const fluency = ref("");

watch(
  () => aiClassStData?.value,
  (val) => {
    nextTick(() => {
      paper(val as Static);
    });
  },
  // { deep: true }
  { immediate: true }
);

const paper = (val: Static) => {
  if (!val.speak) return;
  const speak = JSON.parse(val.speak);
  if (speak.teacher) {
    start(speak.teacher.speedData);
    fluency.value = speak.teacher.fluency;
  } else if (speak.student) {
    start(speak.student.speedData);
    fluency.value = speak.student.fluency;
  }

  if (speak.teacher) {
    teachaerAverageSpeed.value = speak.teacher.averageSpeed.toFixed(2);
    teachaerTotalMinutes.value = speak.teacher.totalMinutes.toFixed(2);
  } else {
    teachaerAverageSpeed.value = "0";
    teachaerTotalMinutes.value = "0";
  }
  if (speak.student) {
    studentAverageSpeed.value = speak.student.averageSpeed.toFixed(2);
    studentTotalMinutes.value = speak.student.totalMinutes.toFixed(2);
  } else {
    studentAverageSpeed.value = "0";
    studentTotalMinutes.value = "0";
  }
};

const trackLeft = computed(() => {
  const p = (Number(teachaerAverageSpeed.value) / 400) * 100;
  if (p > 100) return "100%";
  return p + "%";
});
const trackBg = computed(() => {
  const p = (Number(teachaerAverageSpeed.value) / 400) * 100;
  if (p > 80) {
    return "#FA541C";
  } else if (p > 60) {
    return "#9FA624";
  } else if (p > 40) {
    return "#6AD429";
  } else if (p > 20) {
    return "#44B89F";
  } else if (p > 0) {
    return "#27A3FA";
  }
});
</script>

<template>
  <div class="speech-rate">
    <div class="content">
      <div class="percentage">
        <div
          class="track-tip"
          :style="{
            left: trackLeft,
          }"
        >
          <div class="track-tip-line"></div>
        </div>
        <div class="track"></div>
        <div class="track-line">
          <span>0</span>
          <span>200</span>
          <span>400</span>
        </div>
      </div>
      <div>
        平均语速
        {{ teachaerAverageSpeed }} 字/分钟
      </div>
      <span v-if="fluency">流利度：<span style="color: #007AFF">{{ fluency }}</span></span>
    </div>
    <div class="body">
      <div id="speechChart"></div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.speech-rate {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}
.percentage {
  display: inline-block;
  margin-top: 12px;
  padding-top: 10px;
  position: relative;
  .track {
    position: relative;
    border-radius: 6px;
    width: 240px;
    height: 12px;
    background: linear-gradient(
      270deg,
      #fa541c 0%,
      #6ad429 48%,
      #27a3fa 100%
    );
  }
  .track-tip {
    z-index: 10;
    position: absolute;
    top: 0;
    width: 0;
    height: 0;
    border: 8px solid v-bind(trackBg);
    border-left-color: transparent;
    border-bottom-color: transparent;
    border-right-color: transparent;
    transform: translateX(-8px);
    .track-tip-line {
      position: absolute;
      border-left: 1px dashed #fff;
      top: 0;
      height: 14px;
      left: 0px;
      width: 1px;
    }
  }
  .track-line {
    width: 240px;
    color: #8c8c8c;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.percentageStatus {
  overflow: hidden;
  margin-left: 32px;
  width: 144px;
  height: 32px;

  border-radius: 32px;
  display: flex;
  align-items: center;
  color: #fff;
  .iconBox {
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 32px 32px 0 32px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.body {
  flex: 1;
  height: 0;
  display: flex;
  flex-direction: column;
  margin-top: 20px;
  .title {
    margin-bottom: 10px;
    font-weight: 700;
  }
  #speechChart {
    /* flex: 1; */
    width: 100%;
    height: 186px;
  }
}
</style>
