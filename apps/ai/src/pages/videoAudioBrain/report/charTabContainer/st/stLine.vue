<script lang="ts" setup>
import { onMounted, ref, watch, onUnmounted, inject, Ref, nextTick } from "vue";
import * as echarts from "echarts";
import { Static } from "./../../../entity";

// const stInfo = inject<Ref<Static>>("stInfo");
const aiClassStData = inject<Ref<Static>>("aiClassStData");

let chart: any = null;

function start(list: string[]) {
  const data: any = [[0, 0]];
  let lastX = 0;
  let lastY = 0;
  for (let i = 0; i < list.length; i++) {
    if (list[i] === "S") {
      lastY += 1;
      data.push([lastX, lastY]);
    } else if (list[i] === "T") {
      lastX += 1;
      data.push([lastX, lastY]);
    }
  }
  // for (let i = 0; i < list.length; i++) {
  //   if (list[i] === "S") {
  //     data.push([lastX, lastY]);
  //     lastY = i;
  //     data.push([lastX, lastY]);
  //   } else if (list[i] === "T") {
  //     data.push([lastX, lastY]);
  //     lastX = i;
  //     data.push([lastX, lastY]);
  //   }
  // }

  data.map((item: any) => {
    item[0] = item[0] * (5 / 60);
    item[1] = item[1] * (5 / 60);
  });

  if (!chart) {
    chart = echarts.init(document.getElementById("aiStLine")!);
  }
  let max = Math.max(...data[data.length - 1]);
  max = Math.ceil(max / 10) * 10;
  const option = {
    tooltip: {},
    grid: {
      left: "0",
      top: "10",
      right: "15",
      bottom: "0",
      containLabel: true,
    },

    xAxis: {
      max,
    },
    yAxis: {
      max,
    },
    series: [
      {
        type: "line",
        data,
        showSymbol: false,
        itemStyle: {
          color: "#4C84FF",
        },
      },
    ],
  };
  chart.setOption(option);
}

onMounted(() => {
  window.addEventListener("resize", function () {
    chart && chart.resize();
  });
});
onUnmounted(() => {
  window.removeEventListener("resize", function () {
    chart && chart.resize();
  });
  chart && chart.dispose();
  chart = null;
});

watch(
  () => aiClassStData?.value,
  (val) => {
    if (val) {
      nextTick(() => {
        start(JSON.parse(val.stData));
      });
    }
  },
  {
    immediate: true,
  }
);
</script>

<template>
  <div class="st-line">
    <div id="aiStLine"></div>
    <div class="title">S-T曲线图</div>
    <div class="t">T轴(单位:分钟)</div>
    <div class="s">S轴(单位:分钟)</div>
  </div>
</template>

<style lang="scss" scoped>
.st-line {
  width: 46%;
  height: 0;
  padding-top: 46%;
  position: relative;
}
#aiStLine {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
.title {
  text-align: center;
  position: relative;
  top: 30px;
}
.s {
  position: absolute;
  top: 50%;
  left: -60px;
  transform: translateY(-50%) rotate(270deg);
}
.t {
  position: absolute;
  left: 50%;
  bottom: -28px;
  transform: translate(-50%);
}
</style>
