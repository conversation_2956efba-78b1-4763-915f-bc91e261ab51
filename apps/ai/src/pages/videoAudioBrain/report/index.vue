<script lang="ts" setup>
import { ref, watch, computed, provide, onUnmounted } from "vue";
import { ysIcon } from "@ys/ui";
import { common, CommonRes } from "@ys/tools";
import { useRoute, useRouter } from "vue-router";
import { selectResourceDetailInfo, selectVisitEventInfo } from "../api";
const { getUrlQuery } = common;

const route = useRoute();
const router = useRouter();

const isInIframe = window !== window.top;

const title = ref("");
const captionOpenSpeaker = ref(1);
const objType = ref<number>(1); // 1 活动 2 资源
const aiAuth = ref<number>(1); // 1 教师 2 学生
const classroomType = ref(2); // 1 普通 2 微格 3 电影
const subjectName = ref("");

provide("classroomType", classroomType);
provide("aiAuth", aiAuth);
provide("objType", objType);
provide("captionOpenSpeaker", captionOpenSpeaker);
provide("subjectName", subjectName);

function handle2Back() {
  const bureauId = getUrlQuery("bureauId");
  const jd = getUrlQuery("jd");
  const id = route.params.id;
  if (objType.value == 2) {
    const suffixId = sessionStorage.getItem("suffixId");
    let url = `/yskt/a/#/resource/Layout/resource-list/${id}?suffixId=${suffixId}&bureauId=${bureauId}&jd=${jd}`;
    location.href = url;
  } else if (objType.value == 1) {
    let url = `/yskt/ys/active-player/#/${id}?bureauId=${bureauId}&jd=${jd}`;
    location.href = url;
  }
}

function handle3Back() {
  const bureauId = getUrlQuery("bureauId");
  const jd = getUrlQuery("jd");
  const from = route.query.from;
  if (from == "courseAnalyse1") {
    router.replace(`/ai-course/index`);
  } else if (from == "courseAnalyse2") {
    router.replace(`/analyse-task/course-analyse`);
  } else if (from == "researchPlan") {
    let url = `/yskt/ys/research-online/#/researchPlan?bureauId=${bureauId}&jd=${jd}`;
    location.href = url;
  } else if (from == "researchResources") {
    let url = `/yskt/ys/research-online/#/researchResources?bureauId=${bureauId}&jd=${jd}`;
    location.href = url;
  }
}

function handle2change(type: "1" | "2") {
  const groupId = route.query.groupId;
  const from = route.query.from;
  const id = route.params.id;
  const objType = route.params.objType;
  const aiAuth = route.params.aiAuth;
  const taskType = route.query.taskType;
  if (type === "1") {
    router.push(
      `/active-ai/${id}/${objType}/${aiAuth}?groupId=${groupId}&from=${from}&taskType=${taskType}`
    );
  } else {
    router.push(
      `/active-ai/class-diff/${id}/${objType}/${aiAuth}?groupId=${groupId}&from=${from}&taskType=${taskType}`
    );
  }
}

async function getEventDetailInfo() {
  const result = await selectVisitEventInfo<CommonRes<any>>({
    eventId: route.params.id,
  });
  if (result.data.code === 0) {
    title.value = result.data.data.eventInfo.eventName;
    captionOpenSpeaker.value =
      result.data.data.eventOtherInfo.captionOpenSpeaker;
    subjectName.value = result.data.data.eventOtherInfo.subjectName || "";

    // document.title = title.value; // 设置页面标题
  }
}

async function getResourceDetailInfo() {
  const result = await selectResourceDetailInfo<CommonRes<any>>(
    route.params.id
  );
  if (result.data.code === 0) {
    const data = result.data.data;
    title.value = data.resResource.resourceName;
    document.title = title.value; // 设置页面标题
  }
}

const currentPath = ref("");
const active = computed(() => {
  return currentPath.value.indexOf("class-diff") === -1;
});
watch(
  () => route.path,
  (val) => {
    currentPath.value = val;
    objType.value = Number(route.params.objType);
    aiAuth.value = Number(route.params.aiAuth);
    const suffixId = getUrlQuery("suffixId");
    if (suffixId) {
      sessionStorage.setItem("suffixId", suffixId);
    }
    if (objType.value == 2) {
      // 资源详情
      // getResourceDetailInfo();
    } else if (objType.value == 1) {
      // 活动详情
      const id = route.params.id;
      // 资源中心传过来会为0
      if (id !== "0") {
        getEventDetailInfo();
      }
    }
  },
  { immediate: true }
);
watch(
  () => route.query,
  (val) => {
    classroomType.value = Number(route.query.taskType);
  },
  { immediate: true }
);

onUnmounted(() => {
  document.title = "影像大脑";
});
</script>

<template>
  <div class="ai-brain-report">
    <div class="head" v-if="!isInIframe">
      <div class="head-container">
        <div class="head-title">
          <img
            src="@/pages/videoAudioBrain/assets/robot.svg"
            width="28"
            alt=""
          />
          <span style="margin-left: 8px">影像大脑</span>
        </div>
        <div class="nav" v-if="aiAuth == 1">
          <div @click="handle2change('1')" :class="['nav-item', { active }]">
            课程分析
          </div>
          <div
            @click="handle2change('2')"
            :class="['nav-item', { active: !active }]"
          >
            同课异构
          </div>
        </div>
        <div
          v-if="
            route.query.from == 'courseAnalyse1' ||
            route.query.from == 'courseAnalyse2' ||
            route.query.from == 'researchPlan' ||
            route.query.from == 'researchResources'
          "
          @click="handle3Back"
          class="box"
        >
          <ysIcon class="icon" type="icon-close" />
        </div>
        <div v-else @click="handle2Back" class="box">
          <ysIcon class="icon" type="icon-close" />
        </div>
      </div>
    </div>
    <router-view></router-view>
  </div>
</template>

<style lang="scss" scoped>
.active {
  color: #2997ff;
  border-color: #2997ff;
}
.ai-brain-report {
  min-width: 1440px;
  padding-bottom: 32px;
  .head {
    height: 60px;
    background: #3e8ef7;
  }

  .head-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    width: 90%;
    margin: 0 auto;
    max-width: 1520px;

    .head-title {
      color: #ffffff;
      font-size: 18px;
      font-weight: 700;
    }

    .nav {
      padding: 0 48px;
      flex: 1;
      .nav-item {
        cursor: pointer;
        display: inline-block;
        height: 32px;
        width: 88px;
        border-radius: 30px;
        text-align: center;
        line-height: 32px;
        color: #fff;
        background: transparent;
        margin-right: 20px;
      }
      .nav-item.active,
      .nav-item:hover {
        background: #2677df;
      }
    }

    .box {
      cursor: pointer;
      width: 22px;
      height: 22px;
      background: #ffffff;
      border-radius: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .icon {
        color: #262626;
      }
    }
  }
}
</style>
