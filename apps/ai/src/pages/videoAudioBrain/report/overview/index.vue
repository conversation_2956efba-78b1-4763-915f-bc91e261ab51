<template>
  <div class="overview">
    <div class="overview-title">
      <div class="title2">课程类型</div>
      <div class="type" v-if="aiClassStData">
        {{ finalStyleText(String(aiClassStData.stStyle)) }}
      </div>
      <a-radio-group v-model:value="stMode" style="margin-left: 265px;">
        <a-radio-button :value="1">标准</a-radio-button>
        <a-radio-button :value="2">常模</a-radio-button>
      </a-radio-group>
    </div>
    <st :stMode="stMode" style="height: auto" />

    <div style="margin-top: 84px" class="title2">教师发言情况</div>

    <div class="speech">
      <div class="speech-item speech1">
        <div class="title3">教师发言时长</div>
        <div class="value">
          <span class="num">{{ teachaerTotalMinutes }}</span>
          <span class="text">分钟</span>
        </div>
      </div>
      <div class="speech-item speech2">
        <div class="title3">教师发言占比</div>
        <div class="value">
          <span class="num">{{ trackLeft }}</span>
          <span class="text">%</span>
        </div>
      </div>
      <div class="speech-item speech3">
        <div class="title3">流利度</div>
        <div class="value">
          <span class="num">{{ fluency }}</span>
        </div>
      </div>
      <div class="speech-item speech4">
        <div class="title3">口头禅</div>
        <div class="value">
          <span class="num">{{ ktcWords }}</span>
          <span class="text">次</span>
        </div>
      </div>
      <div v-if="classroomType == 3 && remoteInteraction['指数评级']" class="speech-item speech5">
        <div class="title3" style="display: flex;justify-content: space-between;align-items: center;">
          互动指数
          <a-tooltip placement="bottom" color="white" :overlayStyle="{maxWidth: '520px'}">
            <template #title>
              <h4>远端互动详细分析</h4>
              <div v-html="remoteText" style="font-size: 14px;color: #262626;line-height: 20px;"></div>
            </template>
            <ys-icon class="iconfont" type="iconwenjian2" style="font-size: 18px;color: #007AFF;cursor: pointer;" />
          </a-tooltip>
        </div>
        <div class="value">
          <span class="num">{{ remoteInteraction['指数评级'] }}</span>
        </div>

        <!-- <div class="percentage">
          <div
            class="track-tip"
            :style="{
              left: remoteInteractionTrackLeft,
            }"
          >
            <div class="track-tip-line"></div>
          </div>
          <div class="track"></div>
          <div class="track-line"></div>
        </div> -->
      </div>
    </div>

    <div style="margin-top: 24px" class="title2">教学方法</div>
    <div v-show="!methodList.length" style="position: relative; height: 75px">
      <ysEmpt />
    </div>
    <div class="method">
      <div class="method-item" v-for="item in methodList">
        <img :src="setMethodIcon(item)" />
        <span>{{ item }}</span>
      </div>
      <div
        class="method-item"
        style="visibility: hidden"
        v-for="i in methodRemain"
      >
        <img :src="icon1" />
        <span>test</span>
      </div>
    </div>

    <div style="margin-top: 24px" class="title2">布鲁姆问题分析</div>
    <div v-show="!bloomQuestionList.length" style="position: relative; height: 75px">
      <ysEmpt />
    </div>
    <div class="bloom">
      <div class="bloom-item" v-for="(item, index) in bloomQuestionList" :key="index">
        <ys-icon class="icon" :type="item.icon as string" :style="{ fontSize: '28px', color: item.color }" />
        <span style="margin-top: 12px;">{{ item.text }}</span>
        <span class="num">{{ item.num }}</span>
      </div>
    </div>

    <div style="margin-top: 24px" class="title2">“四何”问题分析</div>
    <div v-show="!fourRaderQuestionList.length" style="position: relative; height: 75px">
      <ysEmpt />
    </div>
    <div class="four-rader">
      <div class="four-rader-item" v-for="(item, index) in fourRaderQuestionList" :key="index">
        <span class="english" :style="{ color: item.color }">{{ item.englishText }}</span>
        <span style="font-weight: bold;">{{ item.text }}</span>
        <span class="num">{{ item.num }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, nextTick, Ref, ref, watch } from "vue";
import { ysEmpt, ysIcon } from "@ys/ui";
import st from "../charTabContainer/st/index.vue";
import icon1 from "./icon1.png";
import icon2 from "./icon2.png";
import icon3 from "./icon3.png";
import icon4 from "./icon4.png";
import icon5 from "./icon5.png";
import icon6 from "./icon6.png";
import icon7 from "./icon7.png";
import icon8 from "./icon8.png";
import icon9 from "./icon9.png";
import { MergedSegmentProps, Static } from "../../entity";
import { finalStyleText } from "@/pages/ai/entity";
const aiClassStData = inject<Ref<Static>>("aiClassStData");
const mergedSegments = inject<Ref<MergedSegmentProps[]>>("mergedSegments");
const roleChats = inject<Ref<any>>("roleChats");
const classroomType = inject<Ref<number>>("classroomType");

const teachaerTotalMinutes = ref("0");
const fluency = ref("");
const teachaerAverageSpeed = ref("0");
const ktcWords = ref(0);

const methodList = ref<string[]>([]);
const methodRemain = ref(0);

const setMethodList = (value: any[]) => {
  let arr: string[] = [];
  value.forEach((item) => {
    if (item["教学方式"] != "无") {
      arr.push(item["教学方式"]);
    }
  });
  let arr2: any = [];
  arr.forEach((item) => {
    if (item.indexOf("/") != -1) {
      let obj_arr = item.split("/");
      obj_arr.forEach((item2) => {
        arr2.push(item2);
      });
    } else if (item.indexOf("、") != -1) {
      let obj_arr = item.split("、");
      obj_arr.forEach((item2) => {
        arr2.push(item2);
      });
    } else {
      arr2.push(item);
    }
  });
  arr2 = Array.from(new Set(arr2));
  methodList.value = arr2;
  methodRemain.value = computedRowToRemain(arr2.length, 5);
};

function computedRowToRemain(length: number, row: number): number {
  const flag = length % row;
  return flag ? row - (length % row) : 0;
}

const setMethodIcon = (val: string) => {
  if (val == "讲授") {
    return icon1;
  } else if (val == "自主学习") {
    return icon2;
  } else if (val == "问答") {
    return icon3;
  } else if (val == "讨论") {
    return icon4;
  } else if (val == "练习") {
    return icon5;
  } else if (val == "任务驱动") {
    return icon6;
  } else if (val == "参观教学") {
    return icon7;
  } else if (val == "现场教学") {
    return icon8;
  } else if (val == "自主学习") {
    return icon9;
  } else {
    return icon8;
  }
};

const trackLeft = computed(() => {
  const p = (Number(teachaerAverageSpeed.value) / 400) * 100;
  if (p > 100) return "100";
  return p.toFixed(1);
});
const paper = (val: Static) => {
  if (!val.speak) return;
  const speak = JSON.parse(val.speak);

  if (speak.teacher) {
    fluency.value = speak.teacher.fluency;
  } else if (speak.student) {
    fluency.value = speak.student.fluency;
  }

  if (speak.teacher) {
    teachaerAverageSpeed.value = speak.teacher.averageSpeed.toFixed(2);
    teachaerTotalMinutes.value = speak.teacher.totalMinutes.toFixed(2);
  } else {
    teachaerAverageSpeed.value = "0";
    teachaerTotalMinutes.value = "0";
  }
};
watch(
  () => aiClassStData?.value,
  (val) => {
    nextTick(() => {
      paper(val as Static);
      ktcWords.value = (val as Static).ktcWords;
    });
  },
  // { deep: true }
  { immediate: true }
);
// watch(
//   () => mergedSegments?.value,
//   (val) => {
//     if (val) {
//       console.log("MergedSegmentProps", val);
//       let arr: string[] = [];
//       (val as MergedSegmentProps[]).forEach((item) => {
//         arr.push(item["教学方式"]);
//       });
//       arr = Array.from(
//         new Set(arr.map((item) => (item.includes("讲授") ? "讲授" : item)))
//       );
//       methodList.value = arr;
//       methodRemain.value = computedRowToRemain(arr.length, 5);
//       onSetQuestion();
//     }
//   },
//   // { deep: true }
//   { immediate: true }
// );

interface AnalysisQuestion {
  text: string;
  num: number;
  color: string;
  icon?: string;
  englishText?: string;
}

const bloomQuestionList = ref<AnalysisQuestion[]>([
  {
    icon: "icondengpao",
    text: "创造",
    num: 0,
    color: "#F1584E"
  },
  {
    icon: "iconpingfen",
    text: "评价",
    num: 0,
    color: "#F48A2E"
  },
  {
    icon: "iconshujutu",
    text: "分析",
    num: 0,
    color: "#FAC42D"
  },
  {
    icon: "iconfangkuai",
    text: "应用",
    num: 0,
    color: "#59BE7F"
  },
  {
    icon: "iconbiaoqing1",
    text: "理解",
    num: 0,
    color: "#4AB4D0"
  },
  {
    icon: "iconcengji1",
    text: "记忆",
    num: 0,
    color: "#8F7BD3"
  },
]);
const onSetBloom = (value: any[]) => {
  value.forEach((item) => {
    switch (item["布鲁姆"]) {
      case "创造":
        bloomQuestionList.value[0].num++;
        break;
      case "评价":
        bloomQuestionList.value[1].num++;
        break;
      case "分析":
        bloomQuestionList.value[2].num++;
        break;
      case "应用":
        bloomQuestionList.value[3].num++;
        break;
      case "理解":
        bloomQuestionList.value[4].num++;
        break;
      case "记忆":
        bloomQuestionList.value[5].num++;
        break;
      default:
        break;
    }
  });
}

const fourRaderQuestionList = ref<AnalysisQuestion[]>([
  {
    text: "若何",
    englishText: "WHAT IF",
    num: 0,
    color: "#ED9093"
  },
  {
    text: "如何",
    englishText: "HOW",
    num: 0,
    color: "#79CCB4"
  },
  {
    text: "为何",
    englishText: "WHY",
    num: 0,
    color: "#4F72EB"
  },
  {
    text: "是何",
    englishText: "WHAT",
    num: 0,
    color: "#917EE5"
  },
])
const onSetFourRader = (value: any[]) => {
  value.forEach((item) => {
    switch (item["四何"]) {
      case "若何":
        fourRaderQuestionList.value[0].num++;
        break;
      case "如何":
        fourRaderQuestionList.value[1].num++;
        break;
      case "为何":
        fourRaderQuestionList.value[2].num++;
        break;
      case "是何":
        fourRaderQuestionList.value[3].num++;
        break;
      default:
        break;
    }
  });
}

const stMode = ref<number>(1); // 1: 标准 2：常模

const remoteInteraction = ref<any>({});
const remoteText = computed(() => {
  const grade = remoteInteraction.value["指数评级"];
  const explanation = remoteInteraction.value["评级说明"];
  const comment = remoteInteraction.value["点评"];
  const improvement = remoteInteraction.value["建议"].join(",");
  return `
    <div>互动指数为${grade}级，${explanation}</div>
    <div>${comment}</div>
    <div>改进时，${improvement}</div>
  `;
})
const remoteInteractionTrackLeft = computed(() => {
  const p = (((Number(remoteInteraction.value["指数评级"]) - 4)) / 6) * 100;

  return p.toFixed(1) + '%';
});

const trackBg = computed(() => {
  const p = (((Number(remoteInteraction.value["指数评级"]) - 4)) / 6) * 100;
  if (p > 80) {
    return "#FA541C";
  } else if (p > 60) {
    return "#9FA624";
  } else if (p > 40) {
    return "#6AD429";
  } else if (p > 20) {
    return "#44B89F";
  } else if (p > 0) {
    return "#27A3FA";
  }
});

watch(
  () => roleChats?.value,
  (newV) => {
    if (newV) {
      const roleChat3 = newV[3] as any[];
      const data19 = roleChat3.find((item) => item.questionType === 19);
      if (data19) {
        const data19Answer = JSON.parse(data19.answer);
        setMethodList(data19Answer);
      }
      const data27 = roleChat3.find((item) => item.questionType === 27);
      if (data27 && data27.answer) {
        const data27Answer = JSON.parse(data27.answer);
        onSetBloom(data27Answer);
        onSetFourRader(data27Answer);
      }
      const data35 = roleChat3.find((item) => item.questionType === 35);
      if (data35 && data35.answer) {
        const data35Answer = JSON.parse(data35.answer);
        remoteInteraction.value = data35Answer;
      }
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/fonts/YouSheBiaoTiHei.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.overview {
  width: 100%;
  height: 908px;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  overflow-y: auto;
  .title2 {
    font-weight: bold;
    font-size: 16px;
    color: #262626;
    line-height: 24px;
  }
  .overview-title {
    display: flex;
    align-items: center;
    .type {
      margin-left: 12px;
      padding: 2px 8px;
      font-size: 13px;
      color: #fff;
      background: #17be6b;
      border-radius: 4px;
      box-sizing: border-box;
    }
  }
  .speech {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    width: 100%;
    .speech-item {
      flex: 1;
      height: 86px;
      padding: 16px 20px;
      box-sizing: border-box;
      border-radius: 4px;
      &.speech1 {
        background: #fff5f4;
        .title3 {
          color: #e95e5e;
        }
        .value {
          .num {
            color: #e95e5e;
          }
          .text {
            color: #e95e5e;
          }
        }
      }
      &.speech2 {
        background: #fff9f1;
        .title3 {
          color: #f9750e;
        }
        .value {
          .num {
            color: #f9750e;
          }
          .text {
            color: #f9750e;
          }
        }
      }
      &.speech3 {
        background: #f1fff9;
        .title3 {
          color: #35c68b;
        }
        .value {
          .num {
            color: #35c68b;
          }
          .text {
            color: #35c68b;
          }
        }
      }
      &.speech4 {
        background: #f3f6ff;
        .title3 {
          color: #006bfa;
        }
        .value {
          .num {
            color: #006bfa;
          }
          .text {
            color: #006bfa;
          }
        }
      }
      &.speech5 {
        background: #EEECFE;
        .title3 {
          color: #7262FD;
        }
        .value {
          .num {
            color: #7262FD;
          }
        }
      }
      .value {
        display: flex;
        align-items: center;
        .num {
          font-size: 20px;
          font-weight: bold;
        }
        .text {
          margin-left: 6px;
        }
      }
    }
  }
  .method {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    row-gap: 20px;
    width: 100%;
    .method-item {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 127px;
      height: 56px;
      box-sizing: border-box;
      background: #f8f8f8;
      border-radius: 4px;
      img {
        margin-top: 4px;
        height: 42px;
      }
      span {
        color: #262626;
      }
    }
  }
  .bloom {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    &-item {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 103px;
      height: 103px;
      border-radius: 4px;
      border: 1px solid;
      &:first-child {
        background: linear-gradient( 180deg, rgba(241,88,78,0.1) 0%, rgba(255,255,255,0.1) 100%);
        border-image: linear-gradient(180deg, rgba(241, 88, 78, 0.1), rgba(241, 88, 78, 0.05)) 1 1;
      }
      &:nth-child(2) {
        background: linear-gradient( 180deg, rgba(244,138,46,0.1) 0%, rgba(255,255,255,0.1) 100%);
        border-image: linear-gradient(180deg, rgba(244, 138, 46, 0.1), rgba(244, 138, 46, 0.05)) 1 1;
      }
      &:nth-child(3) {
        background: linear-gradient( 180deg, rgba(250,205,11,0.1) 0%, rgba(255,255,255,0.1) 100%);
        border-image: linear-gradient(180deg, rgba(250, 205, 11, 0.1), rgba(250, 205, 11, 0.05)) 1 1;
      }
      &:nth-child(4) {
        background: linear-gradient( 180deg, rgba(89,190,127,0.1) 0%, rgba(255,255,255,0.1) 100%);
        border-image: linear-gradient(180deg, rgba(89, 190, 127, 0.1), rgba(89, 190, 127, 0.05)) 1 1;
      }
      &:nth-child(5) {
        background: linear-gradient( 180deg, rgba(74,180,208,0.1) 0%, rgba(255,255,255,0.1) 100%);
        border-image: linear-gradient(180deg, rgba(74, 180, 208, 0.1), rgba(74, 180, 208, 0.05)) 1 1;
      }
      &:last-child {
        background: linear-gradient( 180deg, rgba(143,123,211,0.1) 0%, rgba(255,255,255,0.1) 100%);
        border-image: linear-gradient(180deg, rgba(143, 123, 211, 0.1), rgba(143, 123, 211, 0.05)) 1 1;
      }
      .num {
        position: absolute;
        top: -10px;
        right: -10px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 13px;
        color: #007AFF;
        width: 20px;
        height: 20px;
        line-height: 20px;
        background-color: #E6F6FF;
        border-radius: 50%;
      }
    }
  }
  .four-rader {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    &-item {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 164px;
      height: 56px;
      padding: 0 20px;
      border-radius: 4px;
      background-color: #F8F8F8;
      &:first-child {
        .num {
          background: linear-gradient( 180deg, #476AE1 0%, #7892F2 100%);
          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: -5px;
            width: 0;
            height: 0;
            border-left: 5px solid #2249AF;
            border-top: 5px solid transparent;
          }
        }
      }
      &:nth-child(2) {
        .num {
          background: linear-gradient( 180deg, #62C2A6 0%, #96D8C6 100%);
          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: -5px;
            width: 0;
            height: 0;
            border-left: 5px solid #59A590;
            border-top: 5px solid transparent;
          }
        }
      }
      &:nth-child(3) {
        .num {
          background: linear-gradient( 180deg, #7E69DD 0%, #AA9AEF 100%);
          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: -5px;
            width: 0;
            height: 0;
            border-left: 5px solid #403287;;
            border-top: 5px solid transparent;
          }
        }
      }
      &:last-child {
        .num {
          background: linear-gradient( 180deg, #EA7C82 0%, #F1A8A8 100%);
          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: -5px;
            width: 0;
            height: 0;
            border-left: 5px solid #9D3D3F;
            border-top: 5px solid transparent;
          }
        }
      }
      .english {
        font-family: YouSheBiaoTiHei;
      }
      .num {
        position: absolute;
        top: -5px;
        left: 8px;
        font-size: 13px;
        font-weight: bold;
        color: #FFF;
        width: 22px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 0px 0px 4px 4px;
      }
    }
  }
  .percentage {
    display: inline-block;
    margin-top: 12px;
    padding-top: 10px;
    position: relative;
    width: 100%;
    .track {
      position: relative;
      border-radius: 6px;
      height: 8px;
      background: linear-gradient(
        270deg,
        #fa541c 0%,
        #6ad429 48%,
        #27a3fa 100%
      );
    }
    .track-tip {
      z-index: 10;
      position: absolute;
      top: 0;
      width: 0;
      height: 0;
      border: 8px solid v-bind(trackBg);
      border-left-color: transparent;
      border-bottom-color: transparent;
      border-right-color: transparent;
      transform: translateX(-8px);
      .track-tip-line {
        position: absolute;
        border-left: 1px dashed #fff;
        top: 0;
        height: 14px;
        left: 0px;
        width: 1px;
      }
    }
    .track-line {
      color: #8c8c8c;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
}
</style>
