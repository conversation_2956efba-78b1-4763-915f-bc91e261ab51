<template>
  <div class="behavior-pie-chart">
    <div id="behaviorPieChart" style="width: 100%; height: 300px;"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, Ref, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import { AiClassBehaviorData } from "../../entity";

const aiClassBehaviorData = inject<Ref<AiClassBehaviorData>>("aiClassBehaviorData");
const chartInstance = ref<echarts.ECharts | null>(null);

const behaviorNames: { [key: number]: string } = {
  1: "教师讲授",
  2: "教师板书",
  3: "师生互动",
  4: "小组学习",
  5: "独立学习",
  6: "学生展示",
  7: "学生举手",
  8: "其他"
};

const behaviorColors = [
  "#5B8FF9", // 教师讲授
  "#61DDAA", // 教师板书
  "#65789B", // 师生互动
  "#F6BD16", // 小组学习
  "#F56E53", // 独立学习
  "#78D3F8", // 学生展示
  "#14C9C9", // 学生举手
  "#7262FD"  // 其他
];

// 计算各行为类型数据
const computeBehaviorData = () => {
  if (!aiClassBehaviorData?.value?.behavior) return [];

  const behavior = JSON.parse(aiClassBehaviorData.value.behavior);

  // 统计各行为类型出现次数
  const behaviorCounts: { [key: number]: number } = {
    1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0, 7: 0, 8: 0
  };

  behavior.forEach((type: number) => {
    if (type >= 1 && type <= 8) {
      behaviorCounts[type]++;
    }
  });

  // 转换为饼图数据格式
  const pieData = Object.entries(behaviorCounts).map(([type, count]) => {
    return {
      name: behaviorNames[parseInt(type)],
      value: count
    };
  });

  // 过滤掉数值为0的项
  return pieData.filter(item => item.value > 0);
};

const formatTimeToMinutes = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (remainingSeconds === 0) {
    return `${minutes}分钟`;
  } else {
    return `${minutes}分${remainingSeconds}秒`;
  }
};

// 初始化
const initPieChart = () => {
  const chartDom = document.getElementById("behaviorPieChart");
  if (!chartDom) return;

  if (chartInstance.value) {
    chartInstance.value.dispose();
  }

  chartInstance.value = echarts.init(chartDom);

  const pieData = computeBehaviorData();

  const option = {
    color: behaviorColors,
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `<div style="font-size: 14px; padding: 5px;">
          <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: ${params.color}; margin-right: 5px;"></span>
          ${params.name}: ${formatTimeToMinutes(params.value * 5)} (${(params.percent).toFixed(1)}%)
        </div>`;
      }
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 16,
      icon: 'circle',
      textStyle: {
        fontSize: 14
      },
      formatter: (name: string) => {
        // 根据名称找到对应的数据项，计算百分比
        const dataItem = pieData.find(item => item.name === name);
        if (dataItem) {
          const total = pieData.reduce((sum, item) => sum + item.value, 0);
          const percent = (dataItem.value / total * 100).toFixed(1);
          return `${name} ${percent}%`;
        }
        return name;
      }
    },
    series: [
      {
        type: 'pie',
        radius: '65%',
        center: ['50%', '50%'],
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true
        },
        labelLine: {
          show: true
        }
      }
    ]
  };

  chartInstance.value.setOption(option);
};

watch(
  () => aiClassBehaviorData?.value?.behavior,
  () => {
    if (aiClassBehaviorData?.value?.behavior) {
      initPieChart();
    }
  },
  { immediate: true }
);

onMounted(() => {
  if (aiClassBehaviorData?.value?.behavior) {
    setTimeout(() => {
      initPieChart();
    }, 0);
  }
});

onBeforeUnmount(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});
</script>

<style lang="scss" scoped>
.behavior-pie-chart {
  width: 100%;
  height: 280px;
}
</style>