<template>
  <div class="behavior">
    <template v-if="!aiClassBehaviorData">
      <div>
        <lineTitle title="学生个人行为" />
        <Line />
      </div>

      <div style="margin-top: 24px; width: 100%">
        <lineTitle title="学生群体行为" />
        <stuBehavior
          v-show="isStuAll"
          :isStuAll="isStuAll"
        />
        <div v-show="!isStuAll" style="position: relative; height: 180px">
          <ysEmpt />
        </div>
      </div>
      <div style="margin-top: 24px">
        <lineTitle title="教师行为" />
        <teachBehavior v-show="isTeachAll" :isTeachAll="isTeachAll" />
        <div v-show="!isTeachAll" style="position: relative; height: 180px">
          <ysEmpt />
        </div>
      </div>

      <div style="display: flex; justify-content: space-between;margin-top: 24px">
        <div style="flex: 0 0 49%">
          <lineTitle title="学生群体行为占比" />
          <Pie1 />
        </div>
        <div style="flex: 0 0 49%">
          <lineTitle title="教师行为占比" />
          <Pie2 />
        </div>
      </div>
    </template>

    <template v-else>
      <div style="margin-top: 24px">
        <lineTitle title="行为分析" />
        <behaviorAnalysis />
        <!-- <div v-show="!aiClassBehaviorData.behavior" style="position: relative; height: 180px">
          <ysEmpt />
        </div> -->
      </div>

      <div style="margin-top: 24px">
        <lineTitle title="行为占比" />
        <behaviorRatio />
        <!-- <div v-show="!isTeachAll" style="position: relative; height: 180px">
          <ysEmpt />
        </div> -->
      </div>
    </template>

    <div style="margin-top: 24px;">
      <lineTitle title="课堂发言占比" />
      <speechRatio />
    </div>

    <div style="margin-top: 24px; padding-bottom: 24px">
      <lineTitle title="教师语速分析" :tooltip="tooltip['rate']" />
      <speechRate />
    </div>

    <div
      style="
        display: flex;
        justify-content: space-between;
        margin-top: 24px;
        padding-bottom: 24px;
      "
    >
      <div style="flex: 0 0 49%">
        <lineTitle title="口头禅" :tooltip="tooltip['ktc']" />
        <div style="margin-top: 10px; height: 168px">
          <ktc />
        </div>
      </div>
      <div style="flex: 0 0 49%">
        <lineTitle title="关键词" :tooltip="tooltip['wordCloud']" />
        <div style="margin-top: 10px; height: 168px">
          <wordCloud />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, Ref, inject } from "vue";
import lineTitle from "../../components/lineTitle.vue";
import speechRatio from "../charTabContainer/speechRatio/index.vue";
import speechRate from "../charTabContainer/speechRate/index.vue";
import ktc from "../charTabContainer/ktc/index.vue";
import wordCloud from "../charTabContainer/wordCloud/index.vue";
import Line from "../charTabContainer/behavior/line.vue";
import stuBehavior from "../charTabContainer/behavior/stu-behavior.vue";
import teachBehavior from "../charTabContainer/behavior/teach-behavior.vue";
import Pie1 from "../charTabContainer/behavior/pie1.vue";
import Pie2 from "../charTabContainer/behavior/pie2.vue";
import behaviorAnalysis from "./behaviorAnalysis.vue";
import behaviorRatio from "./behaviorRatio.vue";
import { AiClassActionDataVo, AiClassBehaviorData } from "@/pages/videoAudioBrainOld/entity";
import { ysEmpt } from "@ys/ui";

const aiClassActionDataVo = inject<Ref<AiClassActionDataVo>>(
  "aiClassActionDataVo"
);
const aiClassBehaviorData = inject<Ref<AiClassBehaviorData>>("aiClassBehaviorData");

const isStuAll = computed(() => {
  if (
    aiClassActionDataVo?.value &&
    (aiClassActionDataVo?.value.listen.length > 0 ||
      aiClassActionDataVo?.value.recite.length > 0 ||
      aiClassActionDataVo?.value.reading.length > 0 ||
      aiClassActionDataVo?.value.writing.length > 0 ||
      aiClassActionDataVo?.value.discuss.length > 0 ||
      aiClassActionDataVo?.value.studentOther.length > 0)
  ) {
    return true;
  } else {
    return false;
  }
});

const isTeachAll = computed(() => {
  if (
    aiClassActionDataVo?.value &&
    (aiClassActionDataVo?.value.teach.length > 0 ||
      aiClassActionDataVo?.value.blackboardWriting.length > 0 ||
      aiClassActionDataVo?.value.makeInspectionTour.length > 0 ||
      aiClassActionDataVo?.value.onStageInteraction.length > 0 ||
      aiClassActionDataVo?.value.teacherOther.length > 0)
  ) {
    return true;
  } else {
    return false;
  }
});

interface Tooltip {
  [key: string]: {
    title: string;
    content: string;
  }
}

const tooltip = ref<Tooltip>({
  ktc: {
    title: "口头禅",
    content: "统计课堂中高频语气词和口头禅等，挖掘授课过程中识别语言盲区，提升表达效率，帮助教师逐步构建更精炼、更具针对性的教学话语体系。"
  },
  wordCloud: {
    title: "关键词",
    content: "统计课堂中高频语气词和口头禅等，挖掘授课过程中识别语言盲区，提升表达效率，帮助教师逐步构建更精炼、更具针对性的教学话语体系。"
  },
  rate: {
    title: "教师语速分析",
    content: "标准语速：200-500/分钟",
  }
});
</script>

<style lang="scss" scoped>
.behavior {
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}
</style>
