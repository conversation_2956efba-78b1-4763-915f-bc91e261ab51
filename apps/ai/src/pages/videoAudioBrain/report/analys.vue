<script lang="ts" setup>
import { ref, watch, provide, inject, Ref, onMounted, reactive, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ysIcon, ysLoading } from "@ys/ui";
import { common, CommonRes } from "@ys/tools";
import { message } from "ant-design-vue";
import {
    queryAiTaskGroup,
    queryAiTaskGroupSmallClass,
    selectEventAiTaskGroups,
    exportAiReport,
    exportAiReportNew
} from "./../api";
import docModel from "./_docModelStatic/index.vue";
import videoContainer from "./videoContainer/index.vue";
import portContainer from "./portContainer/index.vue";
import chatTab from "./charTabContainer/index.vue";
import overview from "./overview/index.vue";
import {
    AiCaptionTask,
    AiClassActionData,
    AiClassActionDataVo,
    AiTaskGroupVo,
    MergedSegmentProps,
    AiClassBehaviorData
} from "./../entity";
import QrcodeVue from "qrcode.vue";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { emitter } from "@/utils/mitt";
import { useClipboard } from '@vueuse/core'

const { isCdn, down } = common;
const route = useRoute();
const router = useRouter();

const aiTaskGroupVo = ref<AiTaskGroupVo>();
const aiClassStData = ref<any>({});
const aiCaptionTask = ref<AiCaptionTask>();
const roleChats = ref<any>([]);
const listFormItem = ref<any[]>([]);
const aiClassActionData = ref<AiClassActionData>();
const aiClassActionDataVo = ref<AiClassActionDataVo>();
const mergedSegments = ref<MergedSegmentProps[]>();
const aiClassBlackboardData = ref<any>();
const aiClassSpeakData = ref<any>();
const aiClassBehaviorData = ref<AiClassBehaviorData>();
// 二维码相关
const codeOption = reactive({
    value: "",
    size: 180,
    level: "H",
});
const { copy, isSupported, copied } = useClipboard({ source: codeOption.value, legacy: true });

provide("aiTaskGroupVo", aiTaskGroupVo);
provide("aiClassStData", aiClassStData);
provide("aiCaptionTask", aiCaptionTask);
provide("roleChats", roleChats);
provide("listFormItem", listFormItem);
provide("aiClassActionData", aiClassActionData);
provide("aiClassActionDataVo", aiClassActionDataVo);
provide("mergedSegments", mergedSegments);
provide("aiClassBlackboardData", aiClassBlackboardData);
provide("aiClassSpeakData", aiClassSpeakData);
provide("aiClassBehaviorData", aiClassBehaviorData);
const objType = inject<Ref<number>>("objType");
const aiAuth = inject<Ref<number>>("aiAuth");
const classroomType = inject<Ref<number>>("classroomType");

const hasResult = ref(false);
const groupId = ref("");
const resetLoading = ref(true);
const id = route.params.id;

const noExport = ref(true);
provide("noExport", noExport);

// 影像大脑 1.1 新增
const currentMenu = ref<number>(1);
provide("currentMenu", currentMenu);
const onClickMenu = (index: number) => {
    currentMenu.value = index;
};

const onChangeGroupId = async (val: string) => {
    currentMenu.value = 1;
    const id = route.params.id;
    const objType = route.params.objType;
    const aiAuth = route.params.aiAuth;
    const from = route.query.from;
    const taskType = taskGroupList.value.find(
        (item) => item.id === val
    )!.taskType;
    await router.replace(
        `/active-ai/${id}/${objType}/${aiAuth}?groupId=${val}&from=${from}&taskType=${taskType}`
    );
    docUrl.value = "";
};

async function getInfo2() {
    if (!route.query.groupId) return;
    resetLoading.value = false;
    let result = null;
    if (classroomType?.value == 1 || classroomType?.value == 3) {
        result = await queryAiTaskGroup<CommonRes<any>>({
            groupId: route.query.groupId,
        });
    } else {
        result = await queryAiTaskGroupSmallClass<CommonRes<any>>({
            groupId: route.query.groupId,
        });
    }
    if (result.data.code == 0) {
        groupId.value = route.query.groupId as string;
        resetLoading.value = true;
        const data = result.data.data;
        console.log("data", data);
        hasResult.value = true;
        aiClassStData.value = data.aiClassStData;
        aiCaptionTask.value = data.aiCaptionTask;
        roleChats.value = data.roleChats;
        listFormItem.value = data.listFormItem;
        aiTaskGroupVo.value = data.aiTaskGroupVo;
        aiClassActionData.value = data.aiClassActionData;
        aiClassActionDataVo.value = data.aiClassActionDataVo;
        mergedSegments.value = data.mergedSegments;
        document.title = aiTaskGroupVo.value?.groupName || "影像大脑";
        aiClassBlackboardData.value = data.aiClassBlackboardData;
        aiClassSpeakData.value = data.aiClassSpeakData;
        aiClassBehaviorData.value = data.aiClassBehaviorData;
    }
}
/* 获取AI任务组 */
const taskGroupList = ref<aiTaskGroupItemType[]>([]);
async function getTaskGroup() {
    try {
        const response = await selectEventAiTaskGroups({
            eventId: route.params.id,
        });
        const res = response.data as any;
        if (res.code == 0) {
            taskGroupList.value = res.data.filter(
                (item: aiTaskGroupItemType) => {
                    return item.taskGroupStatus == 1;
                }
            );
        }
    } catch (error) {
        console.error("selectEventAiTaskGroups", error);
    }
}

const loading = ref(false);
const exportType = ref<number>(1);
interface ExportImg {
    type: string;
    img: Blob;
}
async function handle2Export(type: number, callback?: Function) {
    // 方法二
    // noExport.value = false;
    // loading.value = true;
    // const element = document.querySelector(".doc") as HTMLElement;
    // setTimeout(() => {
    //   html2canvas(element, { allowTaint: true, useCORS: true }).then((canvas) => {
    //     const imgData = canvas.toDataURL("image/png");
    //     const pdf = new jsPDF("p", "mm", "a4");
    //     const imgProps = pdf.getImageProperties(imgData);

    //     const pdfWidth = pdf.internal.pageSize.getWidth();
    //     const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    //     let heightLeft = pdfHeight;

    //     const pageHeight = pdf.internal.pageSize.getHeight();
    //     let position = 0;

    //     pdf.addImage(imgData, "PNG", 0, position, pdfWidth, pdfHeight);

    //     heightLeft -= pageHeight;

    //     while (heightLeft >= 0) {
    //       position = position - pageHeight;
    //       pdf.addPage();
    //       pdf.addImage(imgData, "PNG", 0, position, pdfWidth, pdfHeight);
    //       heightLeft -= pageHeight;
    //     }
    //     loading.value = false;
    //     pdf.save("分析报告" + ".pdf");
    //     noExport.value = true;
    //   });
    // }, 1000);
    // 临时===================
    // noExport.value = false;
    // loading.value = true;
    // const analyReport = document.querySelector(".doc") as HTMLElement;
    // html2canvas(analyReport!, {
    //     allowTaint: true,
    //     useCORS: true,
    //     scale: 2,
    // }).then(async function (canvas) {
    //     loading.value = false;
    //     var contentWidth = canvas.width / 2;
    //     var contentHeight = canvas.height / 2;

    //     var pdf = new jsPDF("p", "pt", [
    //         contentWidth + 200,
    //         contentHeight + 160,
    //     ]);

    //     var pageData = canvas.toDataURL("image/jpeg", 1.0);
    //     pdf.addImage(pageData, "JPEG", 100, 80, contentWidth, contentHeight);
    //     pdf.save("分析报告.pdf");
    //     noExport.value = true;
    // });

    if (aiTaskGroupVo.value?.reportFileCreated) {
        docUrl.value = aiTaskGroupVo.value?.reportFileUrl || "";
    }

    exportType.value = type;
    isExport.value = true;
    if (docUrl.value) {
        down(isCdn(docUrl.value), "分析报告.docx");
        isExport.value = false;
        return;
    }

    try {
        // 延迟执行以确保加载状态显示
        await new Promise(resolve => setTimeout(resolve, 100));

        const exportImg = Array.from(document.querySelectorAll<HTMLElement>(".doc .export-img"));
        // 批处理相关变量
        const batchSize = 4; // 每批处理4个元素
        const allResults: ExportImg[] = [];

        // 分批处理图片
        for (let i = 0; i < exportImg.length; i += batchSize) {
            const batch = exportImg.slice(i, i + batchSize);
            const batchPromises = batch.map((child) => {
                return new Promise<ExportImg>((resolve) => {
                    html2canvas(child as HTMLElement, {
                        useCORS: true,
                        allowTaint: false,
                        scale: 1,
                    }).then(function (canvas) {
                        canvas.toBlob(function(blob) {
                            if (blob) {
                                resolve({
                                    type: child.getAttribute('data-type') as string,
                                    img: blob
                                });
                            } else {
                                const emptyBlob = new Blob([''], { type: 'image/png' });
                                resolve({
                                    type: child.getAttribute('data-type') as string,
                                    img: emptyBlob
                                });
                            }
                        }, 'image/png');
                    }).catch(error => {
                        // 发生错误时也要 resolve，避免阻塞流程
                        const emptyBlob = new Blob([''], { type: 'image/png' });
                        resolve({
                            type: child.getAttribute('data-type') as string,
                            img: emptyBlob
                        });
                    });
                });
            });

            // 等待当前批次完成并合并结果
            const batchResults = await Promise.all(batchPromises);
            allResults.push(...batchResults);
        }
        try {
            // 创建 FormData 对象
            const formData = new FormData();
            formData.append('groupId', route.query.groupId as string);

            // 将每个 Blob 添加到 FormData 中
            allResults.forEach((item: any) => {
                // 将 Blob 转换为 File 对象以便设置文件名
                const file = new File([item.img], `${item.type}.png`, { type: 'image/png' });
                formData.append(item.type, file);
            });
            const exportApi = aiClassBehaviorData.value ? exportAiReportNew : exportAiReport;
            const res = await exportApi<CommonRes<any>>(formData);
            if (res.data.code == 0) {
                docUrl.value = res.data.data;
                if (type === 1) {
                    callback && callback();
                } else {
                    down(isCdn(docUrl.value), "分析报告.docx");
                }
            } else {
                message.error(res.data.msg);
            }
        }
        catch (error) {
            console.error('报告导出失败', error);
        }
    } catch (error) {
        console.error('图片处理出错', error);
    } finally {
        isExport.value = false;
    }
}

const handle2Copy = () => {
    if (isSupported) {
        copy(codeOption.value);
        if (copied) {
            message.success('复制成功');
        } else {
            message.error('复制失败');
        }
    } else {
        message.error('复制失败');
    }
}

// 生成报告H5链接
const handleExportH5 = () => {
    const href = window.location.href;
    const h5Href = href.replace(/\/active-ai/, "/active-ai-h5");
    codeOption.value = h5Href + "&tenantId=" + (JSON.parse(localStorage.getItem("tempTenant") as any).id || 0) + "&url=" + docUrl.value;
};

const isExport = ref<boolean>(false);
const docUrl = ref<string>("");

watch(
    () => route.query.groupId,
    (val) => {
        getInfo2();
        const type = Number(route.params.objType);
        const id = route.params.id;
        // 资源中心传过来会为0
        if (type == 1 && id !== "0") {
            getTaskGroup();
        }
    },
    { immediate: true }
);

// 布鲁姆分析 和 四何分析 进行切换
const bloomActive = ref(0);
provide("bloomActive", bloomActive);

emitter.on("on-change-bloom-index", (data: any) => {
  bloomActive.value = data as number;
});

onMounted(() => {
    if (aiAuth?.value && aiAuth?.value == 2) {
        currentMenu.value = 3;
    }
});
</script>

<template>
    <ysLoading :loading="!resetLoading" style="position: fixed" />
    <div class="container" v-if="resetLoading">
        <div class="title" v-if="aiTaskGroupVo">
            <!-- <div class="event-name text-overflow">
        {{ aiTaskGroupVo.groupName || "--" }}
    </div> -->
            <ul
                v-if="aiAuth == 1"
                class="menu"
            >
                <li
                    class="menu-item"
                    :class="{ active: currentMenu == 1 }"
                    @click="onClickMenu(1)"
                >
                    <ysIcon type="icongengduo4" class="icon" />
                    <span class="text">概览</span>
                </li>
                <li
                    v-if="classroomType == 1 || classroomType == 3"
                    class="menu-item"
                    :class="{ active: currentMenu == 2 }"
                    @click="onClickMenu(2)"
                >
                    <ysIcon type="iconbanji1" class="icon" />
                    <span class="text">课堂行为</span>
                </li>
                <li
                    class="menu-item"
                    :class="{ active: currentMenu == 3 }"
                    @click="onClickMenu(3)"
                >
                    <ysIcon type="iconwenjian3" class="icon" />
                    <span class="text">教学内容</span>
                </li>
                <li
                    class="menu-item"
                    :class="{ active: currentMenu == 4 }"
                    @click="onClickMenu(4)"
                >
                    <ysIcon type="iconjiaoshi11" class="icon" />
                    <span class="text">教研分析</span>
                </li>
                <li
                    class="menu-item"
                    :class="{ active: currentMenu == 5 }"
                    @click="onClickMenu(5)"
                >
                    <ysIcon type="iconboshimao1" class="icon" />
                    <span class="text">师训专家分析</span>
                </li>
            </ul>
            <a-space :size="16">
                <div class="event-videos" v-if="objType == 1 && id !== '0'">
                    <a-select
                        :value="groupId"
                        style="width: 240px"
                        placeholder="请选择分组"
                        @change="onChangeGroupId"
                    >
                        <a-select-option
                            :value="item.id"
                            v-for="item in taskGroupList"
                            :key="item.id"
                        >
                            <h3>{{ item.groupName }}</h3>
                            <p
                                style="font-weight: 400; color: #262626"
                                class="text-overflow"
                                :title="item.teacherViewName"
                                v-if="item.teacherViewName"
                            >
                                教师画面：{{ item.teacherViewName }}
                            </p>
                            <p
                                style="font-weight: 400; color: #262626"
                                class="text-overflow"
                                :title="item.studentViewName"
                                v-if="item.studentViewName"
                            >
                                学生画面：{{ item.studentViewName }}
                            </p>
                        </a-select-option>
                    </a-select>
                </div>

                <a-popover trigger="click">
                    <template #content>
                        <div class="qrcode-popover">
                            <qrcode-vue
                                v-if="codeOption.value"
                                id="qrcode"
                                :value="codeOption.value"
                                :size="codeOption.size"
                            />
                            <p>
                                <a-input v-model:value="codeOption.value" disabled placeholder="分享地址" style="width: 200px;margin-right: 20px;" />
                                <a-button type="primary" @click="handle2Copy">复制</a-button>
                            </p>
                        </div>
                    </template>
                    <a-button
                        @click="handleExportH5"
                        type="primary"
                        >分享</a-button
                    >
                </a-popover>
                <a-button
                    @click="handle2Export(2)"
                    type="primary"
                    :loading="exportType === 2 && isExport"
                    >导出报告</a-button
                >
            </a-space>
        </div>

        <div v-if="hasResult">
            <div class="ai-content">
                <div class="l">
                    <template
                        v-if="
                            currentMenu == 1 &&
                            (classroomType == 1 || classroomType == 3)
                        "
                    >
                        <overview />
                    </template>
                    <template v-else>
                        <videoContainer />
                        <chatTab />
                    </template>
                </div>
                <portContainer />
            </div>
        </div>
        <docModel />

        <div v-show="!hasResult" style="text-align: center; padding-top: 100px">
            <img src="./../assets/zempt2.svg" alt="" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.container {
    width: 90%;
    margin: 0 auto;
    max-width: 1520px;

    .title {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        /* .event-name {
      font-weight: 700;
      color: #262626;
      font-size: 16px;
      padding-right: 30px;
    } */

        .menu {
            display: flex;
            align-items: center;
            padding: 0;
            margin: 0;
            list-style: none;
            .menu-item {
                margin-right: 16px;
                display: flex;
                align-items: center;
                padding: 8px 16px;
                box-sizing: border-box;
                background: #f5f5f5;
                border-radius: 900px 900px 900px 900px;
                border: 1px solid transparent;
                cursor: pointer;
                &:hover {
                    background: linear-gradient(
                        90deg,
                        rgba(0, 122, 255, 0.05) 0%,
                        rgba(0, 122, 255, 0.1) 100%
                    );
                    border: 1px solid rgba(0, 122, 255, 0.2);
                }
                &.active {
                    background: linear-gradient(
                        90deg,
                        rgba(0, 122, 255, 0.05) 0%,
                        rgba(0, 122, 255, 0.1) 100%
                    );
                    border: 1px solid rgba(0, 122, 255, 0.2);
                    .icon,
                    .text {
                        color: #007aff;
                    }
                }
                .icon {
                    font-size: 16px;
                    color: #262626;
                }
                .text {
                    margin-left: 10px;
                    color: #262626;
                }
            }
        }
    }

    .ai-content {
        display: flex;
    }
    .ai-content .l {
        /* flex: 1; */
        /* width: 0; */
        width: 748px;
        flex-shrink: 0;
    }
}

.qrcode-popover {
    width: 300px;
    text-align: center;
    p {
        margin-top: 15px;
    }
}
</style>
<style>
@media print {
    @page {
        size: A4 portrait;
    }
}
.share-modal {
    .ant-modal-body {
        text-align: center;
    }
}
</style>
