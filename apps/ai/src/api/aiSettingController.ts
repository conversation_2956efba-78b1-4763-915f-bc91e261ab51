import { request } from "@ys/tools";
const { instance, sd_url } = request;

// 分页查询评分表
export const selectAiForms = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/ai/setting/selectAiForms`,
    params,
  });
};

// 编辑评分表
export const editForm = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/ai/setting/editForm`,
    data,
    method: 'POST',
  });
};

// 停用/启用评分表
export const updateFormStatus = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/ai/setting/updateFormStatus`,
    data,
    method: 'POST',
  });
};

// 删除评分表
export const deleteForm = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/ai/setting/deleteForm`,
    params,
  });
};

// 查询评分表评分项
export const listFormItem = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/ai/setting/listFormItem`,
    params,
  });
};

// 编辑评分表评分项
export const editFormItem = <T>(data: any) => {
  return instance<T>({
    url: `${sd_url}/event/ai/setting/editFormItem`,
    data,
    method: 'POST',
  });
};

// 删除评分表评分项
export const deleteFormItem = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/ai/setting/deleteFormItem`,
    params,
  });
};

// 排序评分表指标项
export const updateFormItemOrder = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/ai/setting/updateFormItemOrder`,
    params,
  });
};

// 导出ai评分表
export const exportAiForm = <T>(params: any) => {
  return instance<T>({
    url: `${sd_url}/event/ai/exportAiForm`,
    params,
    responseType: 'blob',
  });
};
