<template>
  <div style="position: relative; width: 100%; height: 100%">
    <ysEmpt v-if="!props.data" />
    <div v-else class="teachStyleLine" id="TeachStyleLine"></div>
  </div>
</template>

<script setup lang="ts">
import { ysEmpt } from "@ys/ui";
import * as echarts from "echarts";
import { watch, nextTick, Ref, inject } from "vue";
type EChartsOption = echarts.EChartsOption;

const currentYear = inject<Ref<string>>("currentYear");
const yearXLineList = inject<Ref<string[]>>("yearXLineList");

export interface TeachStyleLineProps {
  style1: number[];
  style2: number[];
  style3: number[];
  style4: number[];
}

const props = withDefaults(
  defineProps<{
    data: TeachStyleLineProps | null;
  }>(),
  {
    data: null,
  }
);

const initEcharts = (datas: any[]) => {
  const chartDom = document.getElementById("TeachStyleLine")!;
  const myChart = echarts.init(chartDom);
  let option: EChartsOption;
  option = {
    title: {},
    color: ["#7262FD", "#14C9C9", "#FADB14", "#27A3FA"],
    tooltip: {
      padding: [10, 12, 10, 12],
      trigger: "axis",
      textStyle: {
        color: "#262626",
      },
    },
    legend: {
      orient: "horizontal",
      bottom: "24px",
      itemGap: 24,
      data: [
        { icon: "roundRect", name: "对话性" },
        { icon: "roundRect", name: "练习型" },
        { icon: "roundRect", name: "混合型" },
        { icon: "roundRect", name: "讲授型" },
      ],
      align: "left",
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 14,
      },
      selectedMode: false,
    },
    grid: {
      top: "20px",
      left: "5%",
      right: "5%",
      bottom: "12%",
      containLabel: true,
    },
    graphic: {
      type: "text",
      left: "5%", // 文字距离左边的位置
      bottom: "12%", // 文字距离底部的位置
      style: {
        text: "(次)", // 显示的文本
        fill: "#8c8c8c", // 文本颜色
        fontSize: 14,
      },
    },
    xAxis: {
      show: true,
      type: "category",
      axisLine: {
        lineStyle: {
          color: "rgba(0,0,0,0.25)",
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        margin: 15,
        fontSize: 14,
        color: "#262626",
      },
      data:
        currentYear?.value == "全部"
          ? yearXLineList?.value
          : [
              "1月",
              "2月",
              "3月",
              "4月",
              "5月",
              "6月",
              "7月",
              "8月",
              "9月",
              "10月",
              "11月",
              "12月",
            ],
    },
    yAxis: {
      show: true,
      type: "value",
      axisLabel: {
        color: "#262626",
        fontSize: 14,
      },
      splitLine: {
        lineStyle: {
          type: "dashed",
          color: "#F0F0F0",
        },
      },
    },
    series: [
      {
        showSymbol: true,
        name: "对话性",
        type: "line",
        data: datas[0],
        symbol: "circle",
        symbolSize: 7,
        itemStyle: {
          borderWidth: 2,
          borderColor: "#FFF",
        },
      },
      {
        showSymbol: true,
        name: "练习型",
        type: "line",
        data: datas[1],
        symbol: "circle",
        symbolSize: 7,
        itemStyle: {
          borderWidth: 2,
          borderColor: "#FFF",
        },
      },
      {
        showSymbol: true,
        name: "混合型",
        type: "line",
        data: datas[2],
        symbol: "circle",
        symbolSize: 7,
        itemStyle: {
          borderWidth: 2,
          borderColor: "#FFF",
        },
      },
      {
        showSymbol: true,
        name: "讲授型",
        type: "line",
        data: datas[3],
        symbol: "circle",
        symbolSize: 7,
        itemStyle: {
          borderWidth: 2,
          borderColor: "#FFF",
        },
      },
    ],
  };
  myChart.setOption(option);
};

watch(
  () => props.data,
  (newV) => {
    if (props.data) {
      const datas = [
        props.data.style3,
        props.data.style1,
        props.data.style4,
        props.data.style2,
      ];
      nextTick(() => {
        initEcharts(datas);
      });
    }
  }
);
</script>

<style lang="scss" scoped>
.teachStyleLine {
  display: inline-block;
  width: 100%;
  height: 484px;
  box-sizing: border-box;
  vertical-align: middle;
}
</style>
