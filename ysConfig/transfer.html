<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>loading...</title>
  </head>

  <body>
    <script>
      function getCookie(key) {
        const cookies = document.cookie;
        const cookieList = cookies.split(";");
        for (let i = 0; i < cookieList.length; i++) {
          const arr = cookieList[i].split("=");
          if (key === arr[0].trim()) {
            return arr[1];
          }
        }
        return "";
      }
      function getUrlQuery(key) {
        const url = window.location.href;
        if (url.indexOf("?") != -1) {
          const baseStr = url.split("?")[1];
          const reArr = baseStr.split("&");
          const map = {};
          reArr.forEach((item) => {
            const [key, value] = item.split("=");
            map[key] = value;
          });
          return map[key];
        }
        return '';
      }
      window.onload = function () {
        const urlMap = {
          learnCenter: "/yskt/learn/#/learn-bootstrap",
          testCenter: "/yskt/learn/#/learn-bootstrap",
          resourceCenter: "/yskt/a/#/newResource/Layout",
          resourceCenterV1: "/yskt/a/#/newResource/Layout",
          resourceCenterV2: "/yskt/a/#/newResource/Layout",
          resourceCenterVIP: "/yskt/a/#/newResource/Layout",
          researchCenter: "/yskt/ys/research-online/#/",
          synclassCenter: "/yskt/a/#/sync/Layout/index",
          liveCenter: "/yskt/ys/live-center/#/",
          studioCenter: "/yskt/a/#/studio/principal/home",
          raceCenter: "/yskt/a/#/race/index",
          knowledgeCenter: "/yskt/knowledge/#/knowledgeStore",
          monitorCenter: "/yskt/monitor/#/display",
          coursetourCenter: "/yskt/coursetour/#/",
          clipCenter: "/yskt/a/#/clip/index",
          certifyCenter: "/yskt/certify/#/list",
          certifySearchCenter: "/yskt/certify/#/pcsearch",
          fileCenter: "/yskt/a/#/uploadCenter",
          scientificCenter: "/yskt/research/#/researchHome/myTopic/declareList",
          manageCenter: "/yskt/managecenter/#/",
          prepareLessonCenter: "/yskt/lessons/#/myLesson",
          speedClassCenter: "/yskt/a/#/fast/index",
          trainCenter: "/yskt/train/#/training",
          h5HelpCenter: "/yskt/h5-helper/#/pc/template-list",
          magicBoxCenter: "/yskt/magic/#/magicBox",
          eduCenter: "/yskt/ys/continue-education/#/client/index",
          robotCenter: "/yskt/ys/probe-robot/#/overview",
          hosCenter: "/yskt/hos",
          signCenter: "/yskt/ys/sign/#/signIndex",
          meetingCenter: "/yskt/ys/meeting-center/#/meetingArguments",
          labDeviceCenter: "/yskt/ys/lab-device/#/",
          tourOfflineCenter: "/yskt/ys/tour-offline/#/",
          aiCenter: "/yskt/ys/ai/#/layout",
        };
        const bureauId = getUrlQuery("bureauId");
        const loginOrgId = getUrlQuery("loginOrgId");
        const unqiueId = getUrlQuery("unqiueId");
        const accesstoken = getUrlQuery("accesstoken");
        let url = urlMap[unqiueId];
        if (!url) return;
        const jd = bureauId ? 1 : 0;

        const cookieToken = getCookie("accessToken");

        if (!cookieToken && !accesstoken) {
          location.href = "/zhxy/#/redirect/login";
          return;
        }
        if(accesstoken) {
          location.href = `${url}?accesstoken=${accesstoken}&bureauId=${bureauId || loginOrgId}&jd=${jd}`;
        }else {
          location.href = `${url}?bureauId=${bureauId || loginOrgId}&jd=${jd}`;
        }
      };
    </script>
  </body>
</html>
